/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { getFileData } from './image';
import { EvidenceFile } from '../components/survey/question/questionInterfaces';
import { InitiativeFile } from '../types/initiative';
import { TKeyValue } from '../types/file';
import { EvidenceLink } from '../types/universalTracker';
import { handleRouteError } from '../logger';

export const cdnUrl = 'https://wwg-cdn.s3.eu-west-2.amazonaws.com';

export interface SubmitQuestionData {
  autoVerify: string;
  valueData: { notApplicableType: string; input: Record<string, never> };
  note?: string;
}

export enum EFileType {
  video = 'video',
  image = 'image',
  pdf = 'application/pdf',
}

export const fileTypeIcon = {
  video: 'fa-video',
  image: 'fa-image',
  'application/pdf': 'fa-file-pdf',
  default: 'fa-file',
};

export const withProfile = (form: TKeyValue) => {
  if (form.files && form.files.length > 0) {
    return { ...form, files: undefined, profile: form.files[0] };
  }
  return form;
};

export const flattenFiles = (form: TKeyValue) => {
  const { files, ...data } = form;
  if (typeof files === 'object') {
    if (Array.isArray(files)) {
      return form;
    } else {
      let flattenForm = data;
      for (const key in files) {
        if (files[key] !== undefined) {
          flattenForm = { ...flattenForm, [key]: files[key] };
        }
      }
      return flattenForm;
    }
  }
};

export const getIconClass = (file: InitiativeFile | undefined): string => {
  if (!file?.metadata) {
    return fileTypeIcon.default;
  }

  const mimetype: EFileType = file.metadata.mimetype as EFileType;
  const matchedType = fileTypeIcon[mimetype];
  if (matchedType) {
    return matchedType;
  }

  const [mediaType] = mimetype.split('/') as EFileType[];
  return fileTypeIcon[mediaType] || fileTypeIcon.default;
};

export const triggerDownload = (document?: { url?: string }) => {
  if (!document || !document.url) {
    return;
  }
  window.open(document.url, '_blank', '');
};

export const resolveFiles = (
  currentFiles: EvidenceFile[],
  addFiles: File[],
  onUpdate: (files: EvidenceFile[]) => void
) => {
  if (!addFiles) {
    return;
  }
  const newFiles = [...currentFiles];
  const data = addFiles.map(getFileData);

  Promise.all(data).then((results) => {
    results.forEach((newFile) =>
      newFiles.push({
        type: 'file',
        file: newFile,
        title: '',
        description: '',
      })
    );

    onUpdate(newFiles);
  });
};

export const removeFiles = (files: EvidenceFile[], index: number, onUpdate: (files: EvidenceFile[]) => void) => {
  const newFiles = [...files];
  newFiles.splice(index, 1);
  onUpdate(newFiles);
};

export const handleEvidenceLinkAdd = (files: EvidenceFile[], link: EvidenceLink, isPublic: boolean = true) => {
  if (!link) {
    return files;
  }
  return [
    ...files,
    {
      type: 'link',
      link: link.url,
      public: isPublic,
      title: '',
      description: link.description,
    },
  ];
};

export const handleFileDescriptionAdd = ({
  evidenceFiles,
  path,
  description,
}: {
  evidenceFiles: EvidenceFile[];
  path?: string;
  description: string;
}) => {
  return evidenceFiles.map((file) => {
    if ('path' in file && file.path === path) {
      file.description = description;
      file.isUpdated = true;
    }
    if ('file' in file && file.file.path === path) {
      file.description = description;
    }
    return file;
  });
};

export const createFormData = (files: { [key: string]: File[] }) => {
  const formData = new FormData();
  for (const key in files) {
    // TODO: support multiple files.
    formData.append(key, files[key][0]);
  }
  return formData;
};

export const readFilePromise = async (filePath: string): Promise<string> => {
  const blob = await fetch(filePath, {
    mode: 'no-cors',
    headers: [],
  }).then((response) => response.blob())
    .catch((e) => {
      handleRouteError(e, {
        message: `Failed to fetch image at path ${filePath}`,
        filePath,
      });
      throw e;
    });

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if ('string' === typeof e?.target?.result) {
        resolve(e.target.result);
      } else {
        const error = new Error('Failed to fetch remote resource');
        handleRouteError(error, {
          debugMessage: `Failed to fetch file at path ${filePath}`,
          filePath,
        });
        reject(error);
      }
    };
    reader.readAsDataURL(blob);
  });
};
