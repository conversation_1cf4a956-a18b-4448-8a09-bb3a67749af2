import { isNumericString } from '@utils/string';

const DEFAULT_DECIMAL_PLACES = 2;

const DIGITS_ONLY_PATTERN = '^[+-]?\\d+$';

export function isDefinedNumber<T = number>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null && value !== '' && !isNaN(value as number);
}

export const generateArrayOfNumbers = (start: number, length: number) => Array.from({ length }, (_, i) => i + start);

export const isNumeric = (n: unknown): n is number => !isNaN(parseFloat(String(n))) && isFinite(Number(n));

export const getDecimalNumber = (value: string | number | undefined | null, decimal: number = 0) => {
  if (!isDefinedNumber(value)) {
    return '';
  }
  return Number.parseFloat(String(value)).toFixed(decimal);
};

export const getDecimalAsNumber = (value: string | number | undefined | null, decimal: number = 0) => {
  const decimalValue = getDecimalNumber(value, decimal);
  return decimalValue ? Number.parseFloat(decimalValue) : decimalValue;
}

export const roundTo = (value: number, decimals: number = DEFAULT_DECIMAL_PLACES) => {
  if (!isFinite(value) || !Number.isInteger(decimals) || decimals < 0) {
    return value;
  }
  const factor = Math.pow(10, decimals);
  return Math.round(value * factor) / factor;
}

export const getDisplayDecimalNumber = (value: number | undefined | null, decimal: number = DEFAULT_DECIMAL_PLACES) => {
  if (!isDefinedNumber(value)) {
    return '';
  }
  return value.toLocaleString('en-GB', {
    minimumFractionDigits: decimal,
    maximumFractionDigits: decimal,
  });
};

export const getDecimalPattern = (decimal: number | undefined) => {
  return (decimal ? `^[+-]?\\d+\\.\\d{${decimal}}$` : DIGITS_ONLY_PATTERN);
};

const formatterSettings = {
  maximumFractionDigits: 2,
};

export const formatNumber = (n: number, settings: Intl.NumberFormatOptions = formatterSettings): string => {
  if (isNaN(n)) {
    return '0';
  }
  return Number(n ?? 0).toLocaleString('en-GB', settings);
};

export const constrainNumber = ({
  value,
  min,
  max,
  fallback,
}: {
  value: number | string | undefined;
  min: number;
  max: number;
  fallback?: number;
}): number | undefined => {
  if (!isNumericString(value)) {
    return fallback;
  }
  const numberValue = Number(value);
  if (numberValue > max) {
    return max;
  }
  if (numberValue < min) {
    return min;
  }
  return numberValue;
};

export const convertValueByDecimal = ({
  value,
  decimal,
}: {
  decimal: number | undefined;
  value: string | number | undefined;
}) => {
  if (!value || !decimal) {
    return value;
  }

  return Number(value).toFixed(decimal);
};
