/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { AppRouteInterface, RouteInterface } from '../../types/routes';
import Nav from './nav';
import { ReportingLevelDropdown } from './ReportingLevelDropdown';
import { Tree } from '../../types/tree';
import { Button } from 'reactstrap';
import { useHistory, useParams, useRouteMatch } from 'react-router-dom';
import { useAppSelector } from '../../reducers';
import { generateUrl } from '../../routes/util';
import { ROUTES } from '../../constants/routes';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { useIsPortfolioTracker } from '../../hooks/useIsPortfolioTracker';
import { AppConfig } from '../../types/app';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { getCurrentUser } from '@selectors/user';
import { getRootOrg } from '@selectors/initiative';
import { isSingleTree } from '@utils/initiative';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface Props {
  appRoute: AppRouteInterface
  appConfig?: AppConfig;
  route: RouteInterface,
  mapRoute?: RouteInterface;
  initiativeTree?: Tree;
}

export const AppHeader = ({ initiativeTree, mapRoute, appRoute, appConfig, route }: Props) => {

  const history = useHistory();
  const currentUser = useAppSelector(getCurrentUser);
  const rootOrg = useAppSelector(getRootOrg);
  const limitReportingLevels = useAppSelector(FeaturePermissions.getLimitReportingLevels);
  const canAccessReportingLevels = limitReportingLevels > 1;

  const { initiativeId = '', portfolioId = '' } = useParams<{ initiativeId?: string, portfolioId?: string }>();

  const isPortfolioTracker = useIsPortfolioTracker();
  const navigateByMapRoute = mapRoute ?? ROUTES.NAVIGATE_BY_MAP;

  const matchedRoute = useRouteMatch(navigateByMapRoute);

  if (!appRoute) {
    return null;
  }

  const canManageRootOrg =
    currentUser && rootOrg ? InitiativePermissions.canManageInitiative(currentUser, rootOrg._id) : false;
  // if user has access to only one initiative, then user must be admin of root org
  const canAccessOrgMap = isSingleTree(initiativeTree) ? canManageRootOrg : true;

  const hasSidebar = appRoute.hasSidebar !== false;
  const onClick = () => {
    if (matchedRoute) {
      // org map is already open, exit
      return;
    }
    history.push(generateUrl(navigateByMapRoute, { initiativeId, portfolioId }));
  };

  const OrgMapIcon = () => {
    if (isPortfolioTracker) {
      return null;
    }
    if (!canAccessOrgMap) {
      return <i className={`fal ${navigateByMapRoute.icon} mr-2 text-ThemeIconSecondary`} />;
    }
    return (
      <SimpleTooltip text='Navigate reporting levels using a visual map'>
        <Button
          className='navigate-by-map-button text-nowrap py-1 px-2'
          color={!canAccessReportingLevels ? 'link-secondary' : 'transparent'}
          onClick={onClick}
          outline
        >
          <i className={`fal ${navigateByMapRoute.icon}`} />
          {!canAccessReportingLevels ? <span className='ms-1'>Org map</span> : ''}
        </Button>
      </SimpleTooltip>
    );
  };

  return (
    <div className={`monitoring-apps-menu-container ${hasSidebar ? 'has-sidebar' : ''}`}>
      <section className='monitoring-apps-menu d-flex flex-column flex-md-row align-items-md-end'>

        <div className='app-nav-container flex-fill order-2 order-md-1 d-flex' data-testid='app-navigation'>
          <div className='app-info-icon text-md-right align-self-center'>
            <img src={appConfig?.logo ?? appRoute.appIcon} alt={appRoute.appName} width={150} />
          </div>
          <div className='ml-3 align-self-end'>
            <Nav appRoute={appRoute} />
          </div>
        </div>
        <div className='d-flex flex-column justify-content-end order-1 order-md-2 align-self-center'>
          <div className='reporting-level-dropdown pb-0 d-flex align-items-center'>
            {appRoute.hasReportingLevels ? (
              <>
                <div className={'ml-auto mr-1'}>
                  <OrgMapIcon />
                </div>
                <ReportingLevelDropdown initiativeTree={initiativeTree} route={route} />
              </>
            ) : null}
          </div>
        </div>
      </section>
    </div>
  )
}
