/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

@import '../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes fadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.header-container {
  position: -webkit-sticky;
  position: sticky;
  top: -3rem;
  z-index: $zindex-sticky;
  @include media-breakpoint-down(xl) {
    position: sticky;
    left: 0;
    z-index: $zindex-sticky;
    &.responsivePage {
      width: inherit;
    }
  }

  .hideOnClose {
    display: none;
    @include media-breakpoint-down(md) {
      display: inline;
      display: initial;
    }
  }

  header {
    background-color: var(--theme-TextWhite);
    .app-header {
      margin-top: 0.4rem;
      border-bottom: 1px solid var(--theme-NeutralsLight);
    }
    .g17eco-header {
      border-bottom: 1px solid var(--theme-NeutralsLight);
      min-height: 2.8rem;
      justify-content: space-between;

      .register-btn,
      .login-btn {
        margin: auto;
        height: 30px;
        transition: $transitionTime;
        padding: 0px 40px;
      }
      .marketplace-btn {
        height: 30px;
        transition: $transitionTime;
        padding: 0px 10px;
      }

      .apps-menu {
        position: relative;
        z-index: 2;

        .apps-menu-blur {
          z-index: 3;
          position: fixed;
          top: 0px;
          left: 0px;
          right: 0px;
          bottom: 0px;
          background-color: rgba(var(--theme-ColourBlack), 0.3);
          backdrop-filter: blur(4px);

          &.visible {
            animation: fadeIn linear 0.1s;
          }
          &.invisible {
            animation: fadeOut linear 0.1s;
          }
        }

        .apps-menu-button {
          z-index: 4;
          height: 2.2rem;
          i {
            font-size: 1.2rem;
          }
        }
        .apps-menu-menu {
          z-index: 4;
          position: absolute;
          right: 0px;
          width: calc(460px + 8rem);
          max-width: calc(100vw - 1rem);
          margin: 0rem 0.2rem 0rem 0.5rem;
          background-color: var(--theme-BgLight);
          border: 1px solid var(--theme-TextPlaceholder);
          border-radius: $borderRadius;
          padding: 2rem 1rem;
          h3 {
            font-family: "oswald";
          }
          @include media-breakpoint-down(md) {
            overflow-y: scroll;
            max-height: 40rem;
          }

          &.visible {
            animation: fadeIn linear 0.2s;
          }
          &.invisible {
            animation: fadeOut linear 0.2s;
          }

          .apps-menu-card {
            width: 100%;
            height: 150px;
            padding: 0.5rem;
            margin: 1rem;
            border-radius: $borderRadius;
            border: 1px solid var(--theme-TextPlaceholder);
            box-shadow: 4px 4px 4px 0px var(--theme-NeutralsLight);

            @include media-breakpoint-up(sm) {
              width: 150px;
            }

            &:hover {
              cursor: pointer;
              background-color: var(--theme-AccentExtralight);
            }
            img {
              max-width: 125px;
              max-height: 125px;
            }
          }
        }
      }

      .g17eco-header-inner {
        height: 2.3rem;
        .g17eco-logo {
          height: 1.6rem;
        }
        .help-buttons {
          padding-right: 0.5rem;
          margin-right: 0.5rem;
          border-right: 1px solid var(--theme-NeutralsLight);
        }
        .user-details {
          height: 2.2rem;
          .avatar-container {
            .avatar {
              margin-top: -0.2rem;
              width: 1.5rem !important;
              height: 1.5rem;
              vertical-align: middle;
              .aspect-ratio-1-1 {
                height: 100%;
                img {
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }
        }
      }
    }

    .navigationMap {
      display: inline-block;
      margin-top: 2px;
      border-radius: $borderRadius;
      display: block;
      min-width: 300px;
      i {
        font-size: 14px;
        width: 30px;
        text-align: center;
        color: var(--theme-TextPlaceholder);
      }
    }

    .reporting-level-dropdown {
      min-width: 300px;
      .navigate-by-map-button {
        height: 2rem;
      }
    }

    .monitoring-apps-menu-container {
      width: 100%;
      max-width: $max-width-container;
      margin-left: auto;
      margin-right: auto;

      &.has-sidebar {
        @include media-breakpoint-up(xxl) {
          flex-direction: row !important;
          max-width: calc(#{$max-width-container} + #{$sidebar-margin} * 2 + #{$sidebar-width}* 2);

          .app-info-icon {
            width: #{$sidebar-width};
          }
        }
      }

      .monitoring-apps-menu {
        max-width: calc(#{$max-width-container} + #{$sidebar-margin} + #{$sidebar-width});
        width: 100%;
        min-height: 48px;
        padding: 0rem 1rem;

        .app-nav-container {
          // Ensure we have some size for app icon to align to
          min-height: 40px;
        }

        .app-info-icon {
          img {
            max-height: 40px;
            max-width: 5rem;
            @include media-breakpoint-up(md) {
              max-height: 52px;
              max-width: 10rem;
            }
          }
        }

        .company-status-icon-outer-container {
          border-radius: $borderRadius;
          background-color: var(--theme-BgToolbar);
          height: 2.1rem;
          vertical-align: top;
          white-space: nowrap;
          padding: 0rem 1.4rem;
          .company-status-icon-container {
            white-space: nowrap;
            display: block;
            i {
              padding-top: 0.2rem;
              font-size: 0.8rem;
            }
            span {
              line-height: 2rem;
              margin-right: 8px;
            }
          }
        }
      }
    }
  }

  &.has-banner {
    .app-header {
      margin-top: 0;
    }
  }
}
