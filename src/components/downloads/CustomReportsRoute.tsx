/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { Route, Switch, useRouteMatch } from 'react-router-dom';
import { useAppSelector } from '../../reducers';
import { SurveyRange, CustomInitiatives, CustomReportType, MetricType, CustomReportTemplate, CreateCustomReportType, CustomReport } from '../../types/custom-report';
import { CustomReportsList } from './CustomReportsList';
import CustomReportManage from './CustomReportManage';
import CustomReportsBreadcrumbs from './CustomReportsBreadcrumbs';
import Dashboard, { DashboardRow } from '../dashboard';
import CustomReportMetricAddForm from './CustomReportMetricAddForm';
import CustomReportMetricAddDisambiguation from './CustomReportMetricAddDisambiguation';
import { rootAppPath, getRootAppPath } from '../../routes/company-tracker/utils';
import { RouteInterfaceMin } from '../../types/routes';
import { ROUTES } from '../../constants/routes';
import { CustomReportCreateDisambiguation } from './CustomReportCreateDisambiguation';
import './styles.scss';
import CustomReportManageSubsidiary from './CustomReportManageSubsidiary';
import CustomReportManageDate from './CustomReportManageDate';
import { Loader } from '@g17eco/atoms/loader';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { UpgradeRequired } from '../../routes/upgrade-required';
import { useGetCustomReportQuery } from '@api/custom-reports';
import { CustomReportTemplateBuilder } from '../../apps/company-tracker/components/custom-report';
import NotFound from '@components/not-found';

const downloadsRoot = `/${rootAppPath}/downloads/:initiativeId?/custom`;
const commonRoute: RouteInterfaceMin = {
  getRootAppPath,
  path: ''
}

export const CustomReportRoutes: { [key: string]: RouteInterfaceMin } = {
  ROOT: {
    ...commonRoute,
    path: `${downloadsRoot}/manage`
  },
  CREATE_REPORT: {
    ...commonRoute,
    path: `${downloadsRoot}/create/:createType?`
  },
  MANAGE_REPORT: {
    ...commonRoute,
    path: `${downloadsRoot}/manage/:reportId`
  },
  ADD_METRIC: {
    ...commonRoute,
    path: `${downloadsRoot}/manage/:reportId/add/:metricType?`
  },
}

const ReportManager = ({
  customReport,
  initiativeId,
}: {
  customReport: CustomReport;
  initiativeId: string;
}) => {
  switch (customReport.type) {
    case CustomReportType.Initiatives:
      return (
        <CustomReportManageSubsidiary customReport={customReport as CustomInitiatives} initiativeId={initiativeId} />
      );
    case CustomReportType.Survey:
    case CustomReportType.SurveyAggregation:
      return <CustomReportManageDate customReport={customReport as SurveyRange} initiativeId={initiativeId} />;
    case CustomReportType.Template:
      return (
        <CustomReportTemplateBuilder
          initiativeId={initiativeId}
          customReport={customReport as CustomReportTemplate}
        />
      );
    case CustomReportType.Metrics:
    default:
      return <CustomReportManage customReport={customReport} initiativeId={initiativeId} />;
  }
};
const CustomReportsRoute = () => {
  const reportMatch = useRouteMatch<{
    initiativeId?: string,
    action: string,
    reportId?: string,
  }>(ROUTES.DOWNLOADS_CUSTOM);
  const reportId = reportMatch?.params.reportId;
  const initiativeId = reportMatch?.params.initiativeId;
  const action = reportMatch?.params.action;

  const canAccessCombinedReport = useAppSelector(FeaturePermissions.canAccessCombinedReport);

  const { data: customReport, isFetching } = useGetCustomReportQuery(
    { initiativeId: initiativeId ?? '', reportId: reportId ?? '' },
    { skip: action === 'create' ||!initiativeId || !reportId }
  );

  if (!initiativeId) {
    return <NotFound />;
  }

  if (!canAccessCombinedReport) {
    return <UpgradeRequired />
  }

  return (
    <Dashboard className='custom-report-root'>
      <DashboardRow>
        <Route path={`${ROUTES.DOWNLOADS_CUSTOM.path}/:action?/:metricType?`}>
          <CustomReportsBreadcrumbs customReport={customReport} />
        </Route>
      </DashboardRow>
      { isFetching ? <Loader /> : null}
      {!customReport
        ?
        <Switch>
          <Route
            path={CustomReportRoutes.CREATE_REPORT.path}
            render={(props) => {
              const initiativeId = props.match.params.initiativeId;
              if (!initiativeId) {
                return <NotFound />;
              }
              return (
                <CustomReportCreateDisambiguation
                  initiativeId={initiativeId}
                  createType={props.match.params.createType as CreateCustomReportType | undefined}
                />
              );
            }}
          ></Route>
          <Route>
            <CustomReportsList initiativeId={initiativeId}/>
          </Route>
        </Switch>
        :
        <Switch>
          <Route path={CustomReportRoutes.ADD_METRIC.path} render={(props) => {
            if (props.match.params.metricType) {
              return (
                <CustomReportMetricAddForm
                  type={props.match.params.metricType as MetricType}
                  customReport={customReport}
                />
              );
            }
            return <CustomReportMetricAddDisambiguation customReport={customReport} />
          }} />
          <Route path={CustomReportRoutes.MANAGE_REPORT.path}>
            <ReportManager customReport={customReport} initiativeId={initiativeId} />
          </Route>
        </Switch>
      }
    </Dashboard>
  );
}

export default CustomReportsRoute;
