import { UtrvVariationWarning } from '@g17eco/types/question';
import React, { createContext, useContext } from 'react';
import { VariationWarningMessage } from './VariationWarningMessage';

interface VariationContextProps {
  getVariationWarningMessage: (valueListCode?: string) => React.ReactNode;
}

export const VariationContext = createContext<VariationContextProps>({
  getVariationWarningMessage: () => null,
});

export const useVariationContext = () => {
  return useContext(VariationContext);
};

interface Props {
  children: JSX.Element;
  variationWarnings: UtrvVariationWarning[];
}

export const VariationContextProvider = ({ children, variationWarnings }: Props) => {
  const getVariationWarningMessage = (valueListCode?: string) => {
    const variationWarning = valueListCode
      ? variationWarnings.find((warning) => warning.valueListCode === valueListCode)
      : variationWarnings[0];
    if (!variationWarning) {
      return null;
    }
    return <VariationWarningMessage variationWarning={variationWarning} />;
  };

  return (
    <VariationContext.Provider value={{ getVariationWarningMessage }}>
      {children}
    </VariationContext.Provider>
  );
};
