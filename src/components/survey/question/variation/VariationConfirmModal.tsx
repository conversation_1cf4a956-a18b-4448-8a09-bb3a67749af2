import { UpdateActions } from '@actions/universalTrackerValue';
import { SURVEY } from '@constants/terminology';
import { UtrvVariationWarning } from '@g17eco/types/question';
import { <PERSON><PERSON>, <PERSON>dal, <PERSON>dalB<PERSON>, <PERSON><PERSON>Footer, ModalHeader } from 'reactstrap';

const { VERIFY, UPDATE, REJECT } = UpdateActions.STATUS;

const CONFIRMATION_TEXT = {
  [UPDATE]: 'Are you sure you want to submit this value?',
  [VERIFY]: 'Are you sure you want to verify this submission?',
};

const CONFIRMATION_ACTION = {
  [UPDATE]: 'Submit',
  [VERIFY]: 'Verify',
  [REJECT]: 'Reject',
};

const getParagraphPrefix = (action: string) => {
  if (action === VERIFY) {
    return 'You are about to verify a value';
  }
  return 'The value entered';
};

export interface VariationConfirmModalProps {
  action: string;
  handleClose: () => void;
  handleSubmit: () => void;
  variationWarnings: UtrvVariationWarning[];
}

export const VariationConfirmModal = (props: VariationConfirmModalProps) => {
  const { variationWarnings, handleClose, handleSubmit, action } = props;

  if (!variationWarnings || variationWarnings.length === 0) {
    return null;
  }

  const { valueListCode, formattedBaseline, formattedCurrentInput, baselineReportingDate, variance } =
    variationWarnings[0];

  const onSubmit = () => {
    handleSubmit();
    handleClose();
  };

  return (
    <Modal isOpen={!!action} toggle={handleClose} backdrop='static'>
      <ModalHeader toggle={handleClose}>Variation detected</ModalHeader>
      <ModalBody>
        <div>
          <strong>Warning: </strong>
          {valueListCode ? (
            <span>
              Some of the values entered for this metric fall outside of the variance limit set by the admin in the{' '}
              {baselineReportingDate} {SURVEY.SINGULAR}.
            </span>
          ) : (
            <span>
              {getParagraphPrefix(action)} <strong>{formattedCurrentInput}</strong> falls outside the {variance}%
              variance limit set by the admin. The baseline value for this metric was{' '}
              <strong>{formattedBaseline}</strong> set in{' '}
              {baselineReportingDate}.
            </span>
          )}
        </div>
        <div className='mt-3'>{CONFIRMATION_TEXT[action]}</div>
      </ModalBody>
      <ModalFooter>
        <Button color='transparent' onClick={handleClose}>
          Cancel
        </Button>
        <Button color='primary' onClick={onSubmit}>
          {CONFIRMATION_ACTION[action]}
        </Button>
      </ModalFooter>
    </Modal>
  );
};
