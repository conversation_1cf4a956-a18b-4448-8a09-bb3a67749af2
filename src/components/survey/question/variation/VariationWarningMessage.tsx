import { FeatureStability } from '../../../../molecules/feature-stability';
import { UtrvVariationWarning } from '../../../../types/question';

export const VariationWarningMessage = ({ variationWarning }: { variationWarning: UtrvVariationWarning }) => {
  const { variance, formattedBaseline, baselineReportingDate } = variationWarning;

  const message =
    `The value entered falls outside of the ${variance}% variance limit set by the admin. ` +
    `The baseline value for this metric was ${formattedBaseline} set in ${baselineReportingDate}.`;

  return (
    <div className='text-ThemeDangerMedium'>
      <FeatureStability stability='beta' />
      <i className='fal fa-chart-waterfall mx-2' />
      <strong className='mr-1'>Warning:</strong>
      {message}
    </div>
  );
};
