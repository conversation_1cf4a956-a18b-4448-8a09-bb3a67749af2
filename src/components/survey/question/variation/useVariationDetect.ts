import { useGetUtrvVariationsQuery } from '@api/utrv';
import { hasOverriddenVariations } from '@features/question-configuration';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { CurrentInputData } from '@g17eco/types/question';
import { UniversalTrackerPlain } from '@g17eco/types/universalTracker';
import { UnitConfig } from '@models/surveyData';
import { skipToken } from '@reduxjs/toolkit/query';
import { useState } from 'react';
import { getUtrvVariationWarnings } from '../../utils/input';
import { VariationConfirmModalProps } from './VariationConfirmModal';

interface Props {
  utrvId?: string;
  utr?: UniversalTrackerPlain;
  initiativeUtr?: InitiativeUniversalTracker;
  currentInputData: CurrentInputData;
  unitConfig?: UnitConfig;
}

export const useVariationDetect = ({ utrvId, utr, initiativeUtr, currentInputData, unitConfig }: Props) => {
  const hasVariations = initiativeUtr && hasOverriddenVariations(initiativeUtr);
  const { data: utrvVariations } = useGetUtrvVariationsQuery(hasVariations && utrvId ? { utrvId } : skipToken);
  const [variationConfirmModalProps, setVariationConfirmModalProps] = useState<VariationConfirmModalProps>();

  const variationWarnings = getUtrvVariationWarnings({
    currentInputData,
    unitConfig,
    utr,
    utrvVariations,
  });

  const handleClose = () => {
    setVariationConfirmModalProps(undefined);
  };

  const handleOpenVariationConfirmModal = ({
    action,
    handleSubmit,
  }: Pick<VariationConfirmModalProps, 'action' | 'handleSubmit'>) => {
    const variationConfirmRequired =
      variationWarnings.length > 0 && variationWarnings.some(({ confirmationRequired }) => confirmationRequired);
    if (variationConfirmRequired) {
      setVariationConfirmModalProps({ action, handleSubmit, handleClose, variationWarnings });
      return;
    }
    handleSubmit?.();
  };

  return { variationWarnings, variationConfirmModalProps, handleOpenVariationConfirmModal };
};
