/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useLayoutEffect, useRef, useState, RefObject } from 'react';

interface UseExpandableContentProps {
  defaultHeight: number;
  enableExpansion?: boolean;
  dependencies?: unknown[];
}

interface UseExpandableContentReturn {
  contentRef: RefObject<HTMLDivElement>;
  isHidden: boolean;
  toggleExpansion: () => void;
  isTruncated: boolean;
  height: string;
  className: string;
  containerStyle: React.CSSProperties | undefined;
}

/**
 * Shared hook for expandable content functionality
 * Handles height measurement, state management, and styling for read more/less components
 */
export const useExpandableContent = ({
  defaultHeight,
  enableExpansion = true,
  dependencies = []
}: UseExpandableContentProps): UseExpandableContentReturn => {
  const [currentHeight, setHeight] = useState(0);
  const [isHidden, setHidden] = useState(true);
  const contentRef = useRef<HTMLDivElement | null>(null);

  useLayoutEffect(() => {
    if (contentRef.current && enableExpansion) {
      setHeight(contentRef.current.scrollHeight || 0);
    }
  }, [enableExpansion, ...dependencies]);

  const isTruncated = currentHeight > defaultHeight;

  const height = isTruncated && isHidden ? `${defaultHeight}px` : `${currentHeight}px`;
  const className = isTruncated ? `is-truncated ${isHidden ? 'collapsed' : 'expanded'}` : '';
  const containerStyle = enableExpansion ? { height } : undefined;

  const toggleExpansion = () => setHidden(prev => !prev);

  return {
    contentRef,
    isHidden,
    toggleExpansion,
    isTruncated,
    height,
    className,
    containerStyle
  };
};

// Legacy hook for DOM query selector approach (used by Instructions component)
interface UseExpandableContentLegacyProps {
  defaultHeight: number;
  selector: string;
  dependencies?: unknown[];
}

interface UseExpandableContentLegacyReturn {
  isHidden: boolean;
  toggleExpansion: () => void;
  isTruncated: boolean;
  height: string;
  className: string;
}

/**
 * Legacy hook for components that use DOM query selectors
 * Maintains backward compatibility with existing Instructions component
 */
export const useExpandableContentLegacy = ({
  defaultHeight,
  selector,
  dependencies: _dependencies = []
}: UseExpandableContentLegacyProps): UseExpandableContentLegacyReturn => {
  const [isHidden, setHidden] = useState(true);

  const textHeight = document.querySelector(selector)?.scrollHeight || defaultHeight;
  const isTruncated = textHeight > defaultHeight;

  const height = (() => {
    switch (true) {
      case isTruncated && isHidden:
        return `${defaultHeight}px`;
      case !isTruncated:
        return '100%';
      default:
        return `${textHeight}px`;
    }
  })();

  const className = (() => {
    if (!isTruncated) return '';
    return isHidden ? 'is-truncated collapsed' : 'is-truncated expanded';
  })();

  const toggleExpansion = () => setHidden(prev => !prev);

  return {
    isHidden,
    toggleExpansion,
    isTruncated,
    height,
    className
  };
};
