/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { useState } from 'react';

interface UseExpandableContentProps {
  defaultHeight: number;
  selector: string;
  dependencies?: unknown[];
}

interface UseExpandableContentReturn {
  isHidden: boolean;
  toggleExpansion: () => void;
  isTruncated: boolean;
  height: string;
  className: string;
}

export const useExpandableContent = ({
  defaultHeight,
  selector,
  dependencies: _dependencies = []
}: UseExpandableContentProps): UseExpandableContentReturn => {
  const [isHidden, setHidden] = useState(true);

  const textHeight = document.querySelector(selector)?.scrollHeight || defaultHeight;
  const isTruncated = textHeight > defaultHeight;

  const height = (() => {
    switch (true) {
      case isTruncated && isHidden:
        return `${defaultHeight}px`;
      case !isTruncated:
        return '100%';
      default:
        return `${textHeight}px`;
    }
  })();

  const className = (() => {
    if (!isTruncated) return '';
    return isHidden ? 'is-truncated collapsed' : 'is-truncated expanded';
  })();

  const toggleExpansion = () => setHidden(prev => !prev);

  return {
    isHidden,
    toggleExpansion,
    isTruncated,
    height,
    className
  };
};
