import { useState } from 'react';
import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { InstructionLink } from './InstructionLink';
import { ReadMoreButton } from './ReadMoreButton';
import './styles.scss';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const {
    editorState,
    link: instructionsLink,
    label: instructionsText,
    size,
    defaultHeight = 150
  } = props;

  const [isHidden, setHidden] = useState(true);

  // Use DOM query selector to get height, following the Instructions component pattern
  const textHeight = document.querySelector('.readonly-instructions-editor')?.scrollHeight || defaultHeight;
  const isTruncated = textHeight > defaultHeight;

  const height = (() => {
    switch (true) {
      case isTruncated && isHidden:
        return `${defaultHeight}px`;
      case !isTruncated:
        return '100%';
      default:
        return `${textHeight}px`;
    }
  })();

  const className = (() => {
    if (!isTruncated) return '';
    return isHidden ? 'is-truncated collapsed' : 'is-truncated expanded';
  })();

  const toggleExpansion = () => setHidden((prev) => !prev);

  return (
    <div className='expandable-instructions-editor'>
      <div
        className={`readonly-instructions-editor ${className}`.trim()}
        style={{ height }}
      >
        <ReadonlyRichTextEditor editorState={editorState} />
      </div>
      {isTruncated && (
        <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
      )}
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </div>
  );
};
