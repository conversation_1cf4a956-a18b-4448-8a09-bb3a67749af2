import { useLayoutEffect, useRef, useState } from 'react';
import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { Button } from 'reactstrap';
import { InstructionLink } from './InstructionLink';
import { useTextCollapse } from './useTextCollapse';
import './styles.scss';

const ReadMoreButton = ({ isHidden, onToggle }: { isHidden: boolean; onToggle: () => void }) => (
  <Button color='link-secondary' onClick={onToggle}>
    <div className='text-sm py-1'>
      <i className={`fa fa-angle-${isHidden ? 'down' : 'up'} mr-1 text-sm`} />
      {isHidden ? 'Read more' : 'Read less'}
    </div>
  </Button>
);

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
  enableExpansion?: boolean;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const {
    editorState,
    link: instructionsLink,
    label: instructionsText,
    size,
    defaultHeight = 150,
    enableExpansion = true
  } = props;

  const [currentHeight, setHeight] = useState(0);
  const [isHidden, setHidden] = useState(true);
  const editorRef = useRef<HTMLDivElement | null>(null);

  useLayoutEffect(() => {
    if (editorRef.current && enableExpansion) {
      setHeight(editorRef.current.scrollHeight || 0);
    }
  }, [editorState, enableExpansion]);

  const { height, className, isTruncated } = useTextCollapse({
    isHidden,
    height: currentHeight,
    defaultHeight,
  });

  const toggleExpansion = () => setHidden(prev => !prev);

  const containerClassName = enableExpansion ? 'expandable-instructions-editor' : '';
  const editorStyle = enableExpansion ? { height } : undefined;

  return (
    <div className={containerClassName}>
      <ReadonlyRichTextEditor
        ref={enableExpansion ? editorRef : undefined}
        editorState={editorState}
        className={enableExpansion ? className : ''}
        style={editorStyle}
      />
      {isTruncated && enableExpansion && (
        <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
      )}
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </div>
  );
};
