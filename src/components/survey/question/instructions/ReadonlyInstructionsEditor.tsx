import { ReadonlyRichTextEditor } from '@features/rich-text-editor';
import { EditorState } from 'lexical';
import { InstructionLink } from './InstructionLink';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const { editorState, link: instructionsLink, label: instructionsText, size } = props;

  return (
    <>
      <ReadonlyRichTextEditor editorState={editorState} />
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </>
  );
};
