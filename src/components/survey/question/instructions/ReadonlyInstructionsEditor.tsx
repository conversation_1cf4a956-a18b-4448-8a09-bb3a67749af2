import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { InstructionLink } from './InstructionLink';
import { ReadMoreButton } from './ReadMoreButton';
import { useExpandableContent } from './useExpandableContent';
import './styles.scss';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const {
    editorState,
    link: instructionsLink,
    label: instructionsText,
    size,
    defaultHeight = 150
  } = props;

  const {
    isHidden,
    toggleExpansion,
    isTruncated,
    height,
    className
  } = useExpandableContent({
    defaultHeight,
    selector: '.instruction-text',
    dependencies: [editorState]
  });

  return (
    <>
      <div
        className={`instruction-text ${className}`.trim()}
        style={{ height }}
      >
        <ReadonlyRichTextEditor editorState={editorState} />
      </div>
      {isTruncated && (
        <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
      )}
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </>
  );
};
