import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { InstructionLink } from './InstructionLink';
import { useExpandableContent } from './useExpandableContent';
import { ReadMoreButton } from './ReadMoreButton';
import './styles.scss';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
  enableExpansion?: boolean;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const {
    editorState,
    link: instructionsLink,
    label: instructionsText,
    size,
    defaultHeight = 150,
    enableExpansion = true
  } = props;

  const {
    contentRef,
    isHidden,
    toggleExpansion,
    isTruncated,
    className,
    containerStyle
  } = useExpandableContent({
    defaultHeight,
    enableExpansion,
    dependencies: [editorState]
  });

  const containerClassName = enableExpansion ? 'expandable-instructions-editor' : '';

  return (
    <div className={containerClassName}>
      <ReadonlyRichTextEditor
        ref={enableExpansion ? contentRef : undefined}
        editorState={editorState}
        className={enableExpansion ? className : ''}
        style={containerStyle}
      />
      {isTruncated && enableExpansion && (
        <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
      )}
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </div>
  );
};
