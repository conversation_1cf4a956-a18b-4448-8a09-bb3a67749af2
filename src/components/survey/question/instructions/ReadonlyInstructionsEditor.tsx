import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { InstructionLink } from './InstructionLink';
import { useExpandableContentLegacy } from './useExpandableContent';
import { ReadMoreButton } from './ReadMoreButton';
import './styles.scss';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
  enableExpansion?: boolean;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const {
    editorState,
    link: instructionsLink,
    label: instructionsText,
    size,
    defaultHeight = 150,
    enableExpansion = true
  } = props;

  const {
    isHidden,
    toggleExpansion,
    isTruncated,
    height,
    className
  } = useExpandableContentLegacy({
    defaultHeight,
    selector: '.readonly-instructions-editor',
    dependencies: [editorState]
  });

  const containerClassName = enableExpansion ? 'expandable-instructions-editor' : '';
  const editorStyle = enableExpansion ? { height } : undefined;

  return (
    <div className={containerClassName}>
      <ReadonlyRichTextEditor
        editorState={editorState}
        className={`readonly-instructions-editor ${enableExpansion ? className : ''}`.trim()}
        style={editorStyle}
      />
      {isTruncated && enableExpansion && (
        <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
      )}
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </div>
  );
};
