import { useRef, useState } from 'react';
import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { EditorState } from 'lexical';
import { InstructionLink } from './InstructionLink';
import { ReadMoreButton } from './ReadMoreButton';
import './styles.scss';

interface Props {
  editorState: EditorState;
  link?: string;
  label?: string;
  size?: string;
  defaultHeight?: number;
  enableExpansion?: boolean;
}

export const ReadonlyInstructionsEditor = (props: Props) => {
  const {
    editorState,
    link: instructionsLink,
    label: instructionsText,
    size,
    defaultHeight = 150,
    enableExpansion = true
  } = props;

  const [isHidden, setHidden] = useState(true);
  const editorRef = useRef<HTMLDivElement | null>(null);

  // Use DOM query selector to get height, following the Instructions component pattern
  const textHeight = document.querySelector('.readonly-instructions-editor')?.scrollHeight || defaultHeight;
  const isTruncated = textHeight > defaultHeight;

  const height = (() => {
    switch (true) {
      case isTruncated && isHidden:
        return `${defaultHeight}px`;
      case !isTruncated:
        return '100%';
      default:
        return `${textHeight}px`;
    }
  })();

  const className = (() => {
    if (!isTruncated) return '';
    return isHidden ? 'is-truncated collapsed' : 'is-truncated expanded';
  })();

  const toggleExpansion = () => setHidden(prev => !prev);

  const containerClassName = enableExpansion ? 'expandable-instructions-editor' : '';
  const editorStyle = enableExpansion ? { height } : undefined;

  return (
    <div className={containerClassName}>
      <ReadonlyRichTextEditor
        ref={editorRef}
        editorState={editorState}
        className={`readonly-instructions-editor ${enableExpansion ? className : ''}`.trim()}
        style={editorStyle}
      />
      {isTruncated && enableExpansion && (
        <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
      )}
      <InstructionLink link={instructionsLink} label={instructionsText} size={size} />
    </div>
  );
};
