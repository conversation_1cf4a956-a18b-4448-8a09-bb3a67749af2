/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import React from 'react';
import { ReadonlyInstructionsEditor } from './ReadonlyInstructionsEditor';
import { ReadonlyRichTextEditor } from '@features/rich-text-editor/ReadonlyRichTextEditor';
import { createEditor } from 'lexical';
import { defaultConfig } from '@features/rich-text-editor/constants';

/**
 * Example component demonstrating the separation of concerns:
 * - ReadonlyRichTextEditor: Simple, focused readonly editor
 * - ReadonlyInstructionsEditor: Enhanced with read more/less functionality
 */
export const ReadonlyInstructionsEditorExample = () => {
  // Create a sample editor state with long content
  const createSampleEditorState = (text: string) => {
    const editor = createEditor(defaultConfig);
    const editorState = editor.parseEditorState(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text,
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      })
    );
    return editorState;
  };

  const shortText = "This is a short instruction that won't trigger the read more functionality.";
  
  const longText = `This is a very long instruction that will trigger the read more functionality when the content height exceeds the default height. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo.`;

  const shortEditorState = createSampleEditorState(shortText);
  const longEditorState = createSampleEditorState(longText);

  return (
    <div className="container mt-4">
      <h2>ReadonlyInstructionsEditor Examples</h2>
      
      <div className="row">
        <div className="col-md-6">
          <h4>Short Instructions (No Read More)</h4>
          <div className="border p-3 mb-4">
            <ReadonlyInstructionsEditor 
              editorState={shortEditorState}
              defaultHeight={100}
              link="https://example.com/short"
              label="View Short Instructions"
            />
          </div>
        </div>
        
        <div className="col-md-6">
          <h4>Long Instructions (With Read More)</h4>
          <div className="border p-3 mb-4">
            <ReadonlyInstructionsEditor 
              editorState={longEditorState}
              defaultHeight={100}
              link="https://example.com/long"
              label="View Full Instructions"
            />
          </div>
        </div>
      </div>
      
      <div className="row">
        <div className="col-md-6">
          <h4>Expansion Disabled</h4>
          <div className="border p-3 mb-4">
            <ReadonlyInstructionsEditor 
              editorState={longEditorState}
              enableExpansion={false}
              link="https://example.com/disabled"
              label="View Instructions"
            />
          </div>
        </div>
        
        <div className="col-md-6">
          <h4>Basic ReadonlyRichTextEditor</h4>
          <div className="border p-3 mb-4">
            <ReadonlyRichTextEditor 
              editorState={longEditorState}
            />
            <p className="text-muted mt-2">
              <small>Simple readonly editor without expansion logic</small>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
