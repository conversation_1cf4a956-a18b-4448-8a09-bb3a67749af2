/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ReadonlyInstructionsEditor } from './ReadonlyInstructionsEditor';
import { createEditor } from 'lexical';
import { defaultConfig } from '@features/rich-text-editor/constants';

describe('ReadonlyInstructionsEditor', () => {
  const createEditorState = (text: string) => {
    const editor = createEditor(defaultConfig);
    const editorState = editor.parseEditorState(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text,
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      })
    );
    return editorState;
  };

  it('renders basic instructions editor', () => {
    const editorState = createEditorState('Some instruction text');
    
    render(<ReadonlyInstructionsEditor editorState={editorState} />);

    expect(screen.getByText('Some instruction text')).toBeInTheDocument();
  });

  it('renders instruction link when provided', () => {
    const editorState = createEditorState('Some instruction text');
    
    render(
      <ReadonlyInstructionsEditor 
        editorState={editorState}
        link="https://example.com"
        label="View Instructions"
      />
    );

    expect(screen.getByText('View Instructions')).toBeInTheDocument();
  });

  it('does not render instruction link when not provided', () => {
    const editorState = createEditorState('Some instruction text');
    
    render(<ReadonlyInstructionsEditor editorState={editorState} />);

    expect(screen.queryByRole('button')).not.toBeInTheDocument();
  });

  it('renders with expansion enabled', () => {
    const editorState = createEditorState('Some instruction text');

    const { container } = render(
      <ReadonlyInstructionsEditor editorState={editorState} />
    );

    expect(container.querySelector('.expandable-instructions-editor')).toBeInTheDocument();
  });

  it('uses custom defaultHeight when provided', () => {
    const editorState = createEditorState('Some instruction text');
    
    render(
      <ReadonlyInstructionsEditor 
        editorState={editorState} 
        defaultHeight={200}
      />
    );

    expect(screen.getByText('Some instruction text')).toBeInTheDocument();
  });

  it('renders without read more/less buttons initially', () => {
    const editorState = createEditorState('Short text');
    
    render(<ReadonlyInstructionsEditor editorState={editorState} />);

    // Read more/less buttons depend on content height which is hard to test in jsdom
    expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    expect(screen.queryByText('Read less')).not.toBeInTheDocument();
  });
});
