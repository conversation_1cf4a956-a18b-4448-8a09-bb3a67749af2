/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

interface TextCollapseProps {
  isHidden: boolean;
  height: number;
  defaultHeight: number;
}

export const useTextCollapse = ({ isHidden, height, defaultHeight }: TextCollapseProps) => {
  const isTruncated = height > defaultHeight;

  return {
    height: isTruncated && isHidden ? `${defaultHeight}px` : `${height}px`,
    className: isTruncated ? `is-truncated ${isHidden ? 'collapsed' : 'expanded'}` : '',
    isTruncated
  };
};
