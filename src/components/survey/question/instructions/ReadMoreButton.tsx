/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';

interface ReadMoreButtonProps {
  isHidden: boolean;
  onToggle: () => void;
  variant?: 'default' | 'caret';
  className?: string;
}

/**
 * Shared ReadMoreButton component for consistent read more/less functionality
 * Supports different icon variants to match existing patterns
 */
export const ReadMoreButton = ({ 
  isHidden, 
  onToggle, 
  variant = 'default',
  className = ''
}: ReadMoreButtonProps) => {
  const iconClass = variant === 'caret' 
    ? `fal fa-caret-${isHidden ? 'down' : 'up'}` 
    : `fa fa-angle-${isHidden ? 'down' : 'up'}`;
  
  const text = isHidden ? 'Read more' : 'Read less';
  
  return (
    <Button 
      color='link-secondary' 
      onClick={onToggle}
      className={`text-xs ${className}`.trim()}
    >
      <div className='text-sm py-1'>
        <i className={`${iconClass} mr-1 text-sm`} />
        {text}
      </div>
    </Button>
  );
};
