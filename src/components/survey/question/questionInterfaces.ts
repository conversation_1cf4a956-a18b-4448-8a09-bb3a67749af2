/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import type { JSX } from 'react';
import { InputColumn, TableValueDataData } from '../form/input/table/InputInterface';
import UniversalTracker from '../../../model/UniversalTracker';
import { FileTypes, NewEvidenceFile } from '../../../types/file';
import { DocumentSubType, HistoryDocument } from '../../../types/document';
import { SurveyActionData, SurveyModelMinimalUtrv, UnitConfig } from '../../../model/surveyData';
import { UniversalTrackerValuePlain } from '../../../types/surveyScope';
import { SafeUser, UserMin } from '../../../constants/users';
import { Addons, ErrorMessageType } from '../form/input/InputProps';
import { EditorState } from 'lexical';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';

export interface ExistingEvidenceFile extends HistoryDocument {
  status: string; // E.g: 'updated'
  type: FileTypes;
  link?: string;
  isDeleted?: boolean;
  isUpdated?: boolean;
  ownerSubType?: DocumentSubType;
  ownerId?: string;
}

export type EvidenceFile = ExistingEvidenceFile | NewEvidenceFile;

export interface DisplayCheckBox {
  [key: string]: boolean
}

export enum RowStatus {
  removed = 'removed',
  edited = 'edited',
  added = 'added',
  original = 'original',
}

export interface RowDataInfo<T = InputColumn[]> {
  id: number;
  rowStatus: RowStatus;
  isRemoved?: boolean;
  isEdited?: boolean;
  hasChanged?: boolean;
  data: T;
}

export type ValidationItem = { columnCode: string; type: 'warning' | 'error'; value: string | JSX.Element };
export type ValidationRow = { id: number, items: ValidationItem[] };

export interface TableDataInfo {
  editRowId: number,
  rows: RowDataInfo[]
}

interface Input<T extends ValueDataData = ValueDataData> {
  data?: T;
  table?: TableValueDataData;
  value?: number;
  unit?: string;
  numberScale?: string;
}

type ValueType = number | string | undefined;
/** @deprecated replaced by types/universalTrackerValue **/
export type ValueDataObject = Record<string, ValueType>
/** @deprecated replaced by types/universalTrackerValue **/
export type ValueDataData = string | string[] | ValueDataObject

export interface ValueData<T extends ValueDataData = ValueDataData> {
  data?: T;
  table?: TableValueDataData;
  explain?: string;
  isImported?: boolean;
  notApplicableType?: string;
  input?: Input<T>;
}

export interface PrepareSubmitDataParams {
  notApplicableType: string;
  comments: string;
  editorState?: EditorState;
  numberScale: string | undefined;
  valueData: ValueData;
  utr: UniversalTracker;
  unit: string | undefined;
  autoVerify: boolean;
  utrv: SurveyModelMinimalUtrv;
  files: EvidenceFile[];
  displayCheckbox: DisplayCheckBox;
  value: any;
  table: TableDataInfo
}

export interface InitialResettableQuestionState {
  loaded: boolean;
  displayCheckbox: DisplayCheckBox;
  displayConfirmation: boolean;
  action: string;
  saving: boolean;
  errored?: boolean;
  message?: string;
  inputMessage?: ErrorMessageType;
  comments: string; // old plain text note
  editorState?: EditorState; // rich text note
  files: NewEvidenceFile[];
  existingFiles: ExistingEvidenceFile[];
  details: { loaded: boolean, data: any };
  canContribute: boolean;
  canVerify: boolean;
  isNA: boolean;
  // @TODO: [DECIMAL] This can be set as string through valueSlider
  value: undefined | number;
  valueData: ValueData;
  table: TableDataInfo;
  /** @deprecated no longer used? */
  alternativeCode: string;
  alternativeCodeOverride: string;
  numberScale?: string,
  unit?: string,
  saveId?: string;
}

export interface AssuranceState {
  assurancePortfolio?: { assurances?: any[] };
  assurancePortfolioLoaded: boolean;
  hasPortfolio: boolean;
}

export interface QuestionReducerState extends InitialResettableQuestionState, AssuranceState {
}

export type GlossaryState = Record<string, undefined | { html: string }> | undefined;

export interface QuestionProps extends QuestionReducerState {
  // blueprint: Blueprint,
  index: number;
  hasChanged: boolean;
  utr?: UniversalTracker,
  utrv?: SurveyModelMinimalUtrv,
  isContributor: boolean;
  isVerifier: boolean;
  isOrganizationManager: boolean;
  survey: SurveyActionData;
  update: (data: any) => void;
  updateTable: (table: Partial<TableDataInfo>, error?: ErrorMessageType) => any;
  scrollToRef: () => void;
  glossaryState?: GlossaryState;
  // Redux
  errored?: boolean;
  isAggregate: boolean;

  handleNA?: () => void;
  handleNR?: () => void;
  handleAssuranceAction?: (action: LockAction) => Promise<void>;
  handleReject?: () => void;
  handleComments?: (comments: string) => void;
  hasValueChanged: boolean;

  addons?: Addons;
  isQuestionReadOnly?: boolean;
  initiativeUtr?: InitiativeUniversalTracker;

  /** Represent users for comments **/
  users?: UserMin[];
}

export interface QuestionState {
  errored: boolean;
  message?: string;
}

export interface QuestionChangeMinProps {
  utr: UniversalTracker;
  utrv: Pick<UniversalTrackerValuePlain, '_id' | 'initiativeId' | 'valueData' | 'stakeholders' | 'status'>;
}

export interface QuestionChangeOptionalProps {
  utr?: UniversalTracker;
  utrv?: Pick<UniversalTrackerValuePlain, '_id' | 'valueData' | 'value' | 'status'>;
  surveyData?: { unitConfig?: UnitConfig } | SurveyActionData,
}

export interface ChangeQuestionProps extends QuestionChangeMinProps {
  surveyData: Pick<SurveyActionData, '_id' | 'roles' | 'stakeholders' | 'initiativeId' | 'unitConfig'>;
  user: Pick<SafeUser, '_id' | 'permissions'>;
}

interface SubmitValueData extends ValueData {
  input: Input
}

export interface QuestionSubmitData {
  autoVerify: 'true' | '';
  files: EvidenceFile[];
  comments: string;
  editorState?: EditorState,
  value?: string | number;
  valueData: SubmitValueData;
  unit?: string;
  numberScale?: string;
}

export interface QuestionSubmitFormData {
  autoVerify: 'true' | '';
  existingEvidence: ExistingEvidenceFile[];
  files?: File[];
  evidenceLinks?: EvidenceFile[];
  note?: string;
  editorState?: string;
  value?: string | number;
  valueData?: SubmitValueData;
  unit?: string;
  numberScale?: string;
  filesDescriptions?: string[];
  filesMetadata?: {
    id: number;
    saveToLibrary: boolean;
    description: string;
  }[]
}

export enum LockAction {
  Close = 'close',
  Open = 'open',
}
