/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import {
  conversionParams,
  getConvertedValue
} from '@utils/universalTrackerValue'
import { updateTableDataSubmitValues } from '../form/input/table/TableFormData'
import {
  PrepareSubmitDataParams,
  QuestionSubmitData,
  QuestionSubmitFormData,
  ValueDataObject,
} from './questionInterfaces';
import { UtrValueTypes } from '@utils/universalTracker';

export function prepareSubmitData(props: PrepareSubmitDataParams): QuestionSubmitData {

  const {
    utr,
    utrv,
    value,
    unit,
    numberScale,
    files,
    comments,
    editorState,
    valueData,
    table,
    displayCheckbox,
    autoVerify,
    notApplicableType,
  } = props;

  if (notApplicableType) {
    return {
      autoVerify: autoVerify ? 'true' : '',
      files,
      comments,
      editorState,
      valueData: { notApplicableType, input: {} }
    }
  }

  const conversionData = conversionParams(utrv, utr, {
    value,
    unit,
    numberScale
  })

  const convertedValue = getConvertedValue(conversionData);
  const submitValues: QuestionSubmitData = {
    autoVerify: autoVerify ? 'true' : '', // Convert to string manually, rather than string 'false'
    files,
    comments,
    editorState,
    value: convertedValue,
    unit: conversionData.defaultUnit,
    numberScale: conversionData.defaultNumberScale,
    valueData: {
      input: { value, unit, numberScale },
      data: undefined,
    },
  };

  if (utr.getValueType() === UtrValueTypes.table) {
    return updateTableDataSubmitValues({
      submitValues,
      table,
      universalTracker: utr,
    });
  }

  const valueDataData = valueData.data;
  if (valueDataData) {
    if (typeof valueDataData === 'object' && !Array.isArray(valueDataData)) {
      const dataObject: ValueDataObject = {}
      const inputDataObject: ValueDataObject = {}
      utr.getValueListOptions().forEach((item) => {
        if (displayCheckbox[item.code] && valueDataData[item.code]) {
          dataObject[item.code] = getConvertedValue({
            ...conversionData,
            value: valueDataData[item.code]
          });
          inputDataObject[item.code] = valueDataData[item.code];
        }
      });
      submitValues.valueData.data = dataObject;
      submitValues.valueData.input.data = inputDataObject;

    } else if (utr.hasValueData()) {
      // Text based inputs
      submitValues.valueData.data = valueDataData;
      submitValues.valueData.input.data = valueDataData;
    }
  }
  return submitValues
}

export function convertDataForSubmissions(data: QuestionSubmitData) {
  const formData: QuestionSubmitFormData = {
    existingEvidence: [],
    autoVerify: data.autoVerify,
    filesDescriptions: [],
    filesMetadata: [],
  };
  let index = 0;
  if (data.files && data.files.length > 0) {
    data.files.forEach((file) => {
      if ('_id' in file) {
        return formData.existingEvidence.push(file);
      }

      switch (file.type) {
        case 'link':
          if (!formData.evidenceLinks) {
            formData.evidenceLinks = [];
          }
          formData.evidenceLinks.push(file);
          break;
        case 'file':
          if (!formData.files) {
            formData.files = [];
          }
          if ('file' in file) {
            // This will preserve the order of descriptions and files
            formData.filesDescriptions?.push(file.description ?? '');
            formData.files.push(file.file);
            formData.filesMetadata?.push({
              id: index++,
              saveToLibrary: Boolean(file.saveToLibrary),
              description: file.description ?? '',
            });
          }
          break;
        default:
          break;
      }
    });
  }

  formData.value = data.value;
  formData.unit = data.unit;
  formData.numberScale = data.numberScale;
  if (data.valueData) {
    formData.valueData = data.valueData;
  }
  if (data.comments) {
    formData.note = data.comments;
  }

  if (data.editorState) {
    // Convert to string to preserve number type fields, ex: format
    formData.editorState = JSON.stringify(data.editorState.toJSON());
  }

  return formData;
}
