/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { Button } from 'reactstrap';
import { languages } from '../../custom-metrics/constants';
import { Instructions } from './instructions/Instructions';
import UniversalTracker from '../../../model/UniversalTracker';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { loadGlossary } from '../../../slice/glossarySlice';
import './QuestionInformation.scss';
import { ReadonlyRichTextEditor, useGuardEditorState, RichTextEditorContainer } from '@features/rich-text-editor';
import classnames from 'classnames';
import { GlossaryText } from '../../glossary-text/GlossaryText';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { getInstructionLabel } from '@utils/universalTracker';
import { InstructionLink } from './instructions/InstructionLink';
import { ReadonlyInstructionsEditor } from './instructions/ReadonlyInstructionsEditor';

export interface QuestionInformationProps {
  classes?: {
    container?: string;
    valueLabel?: string;
    instructions?: string;
    instructionsLink?: string;
  };
  utr?: UniversalTracker;
  alternativeCode: string;
}

const QuestionInformationInner = (props: QuestionInformationProps) => {
  const { classes, utr, alternativeCode } = props;

  const dispatch = useAppDispatch();
  const _timeout = React.useRef<ReturnType<typeof setTimeout> | undefined>();
  const [startAltTextAnimation, setStartAltTextAnimation] = useState(false);
  const [selectedLanguage, setLanguage] = useState<string | undefined>(undefined);

  const setSelectedLanguage = (language?: string) => {
    if (!language) {
      setStartAltTextAnimation(false);
      if (_timeout.current) {
        clearTimeout(_timeout.current);
      }
      _timeout.current = setTimeout(() => setLanguage(language), 500);
    } else {
      setLanguage(language);
      setStartAltTextAnimation(true);
    }
  };

  React.useEffect(() => {
    dispatch(loadGlossary());
  }, [dispatch]);

  const glossaryState = useAppSelector((state) => state.glossary.data);

  const valueLabel: string = utr ? utr.getValueLabel(alternativeCode) : '';
  const altValueLabel: string = utr ? utr.getValueLabel(selectedLanguage ?? alternativeCode) : '';
  const languageOptions = utr?.getAlternativeLanguages();

  const instructionsLink = utr?.getInstructionsLink(alternativeCode);
  const instructionsText = getInstructionLabel(alternativeCode);

  const instructionsEditorState = utr?.getInstructionsEditorState(alternativeCode);
  const { guardEditorState } = useGuardEditorState(instructionsEditorState);

  return (
    <div className={classnames('questionInformation', classes?.container)}>
      <div className='questionTitle strong'>
        {languageOptions && languageOptions.length > 0 ? (
          <div className='animated-container'>
            <div className={`buttons-animated ${!startAltTextAnimation ? '' : 'disappear-right'}`}>
              {languages
                .filter((l) => languageOptions.includes(l.code))
                .map((l) => (
                  <Button
                    key={`lang_${l.code}`}
                    color='link'
                    size='sm'
                    className='pl-0 mr-2 language-link'
                    onClick={() => setSelectedLanguage(l.code)}
                  >
                    {l.nativeName}
                    <i className='ml-2 far fa-arrow-alt-circle-right' />
                  </Button>
                ))}
            </div>

            <div className={`buttons-animated ${startAltTextAnimation ? '' : 'disappear-left'}`}>
              <Button color='link' size='sm' className='pl-0 mr-2' onClick={() => setSelectedLanguage()}>
                <i className='far fa-arrow-alt-circle-left mr-2' />
                English
              </Button>
            </div>
          </div>
        ) : null}
        <div className='animated-container'>
          <div
            className={classnames('text-xl questionTitle__heading text-animated', classes?.valueLabel, {
              'disappear-right': startAltTextAnimation,
            })}
          >
            {valueLabel ? (
              <GlossaryText text={valueLabel} glossary={glossaryState} />
            ) : (
              <LoadingPlaceholder height={53} />
            )}
          </div>
          <div
            className={classnames('text-xl questionTitle__heading text-animated', classes?.valueLabel, {
              'disappear-left': !startAltTextAnimation,
            })}
          >
            {altValueLabel ? (
              <GlossaryText text={altValueLabel} glossary={glossaryState} />
            ) : (
              <LoadingPlaceholder height={53} />
            )}
          </div>
        </div>
      </div>
      <div key={utr?.getCode()} className={classnames('mt-3', classes?.instructions)}>
        {instructionsEditorState ? (
          <ReadonlyInstructionsEditor
            editorState={guardEditorState}
            link={instructionsLink}
            label={instructionsText}
            size={classes?.instructionsLink}
          />
        ) : (
          <Instructions
            utr={utr}
            glossaryState={glossaryState}
            alternativeCode={alternativeCode}
            selectedLanguage={selectedLanguage}
            startAltTextAnimation={startAltTextAnimation}
            instructionsLinkSize={classes?.instructionsLink}
          />
        )}
      </div>
    </div>
  );
};

export const QuestionInformation = (props: QuestionInformationProps) => {
  return (
    <RichTextEditorContainer>
      <QuestionInformationInner {...props} />
    </RichTextEditorContainer>
  );
};
