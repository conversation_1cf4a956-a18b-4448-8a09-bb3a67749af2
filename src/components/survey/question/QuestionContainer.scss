/*!
 * Copyright (c) 2020. World Wide Generation Ltd
 */

@import '../../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

.question-view {
  position: relative;
  transition: all .3s;

  .navbar {
    justify-content: space-around;

    .nav-link {
      padding: 0;
    }

    ul {
      li {
        &:last-of-type {
          margin-right: 0px !important;
        }
        &.active button {
          background-color: var(--theme-TextPlaceholder);
          color: white;
        }
        button {
          min-width: 142px;
          min-height: 36px;
        }

        @include media-breakpoint-down(lg) {
          margin-bottom: 0.5rem;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .question-container-breadcrumbs {
    div {
      margin: 1px;
    }
  }

  .sideicons-container {
    .sdg-icons {
      display: flex;
      flex-wrap: wrap;
    }
    img {
      width: 75px;
      height: auto;
      margin: 0.5rem 0.73rem;
      background-color: white;
    }
  }

  .question-wrapper {

    &.draft-view {
      background: $colorColorsGrey200;
      border-radius: 12px;
    }

    .reject-btn {
      color: $colorThemeDangerMedium;
    }
    .questionContainer {
      .question-title-container {
        margin-right: 1.5rem;
        i {
          line-height: 1.6rem;
        }
      }

      .rounded-pill {
        font-weight: initial;
      }

      .questionContainer__privacy-badge {
        color: var(--theme-SuccessDark);
        background-color: var(--theme-BgExtralight) !important;
        border: 1px solid var(--theme-SuccessDark);
        &.private {
          color: var(--theme-AccentDark);
          border-color: var(--theme-AccentDark);
        }
      }

      .questionContainer__status-badge {
        text-align: center;
        background-color: var(--theme-ColourWhite) !important;
        color: var(--theme-AccentDark);
        border: 1px solid var(--theme-AccentDark);
      }

      .questionContainer__partial-assurance-badge {
        text-align: center;
        background-color: var(--theme-ColourWhite) !important;
        color: var(--theme-SuccessMedium);
        border: 1px solid var(--theme-SuccessMedium);
      }

      .question-subtitle-container {
        color: var(--theme-TextPlaceholder);
        margin-top: -1.1875rem;

        // An HTML element with position: fixed will lose its fixed positioning
        // if a CSS transform is applied to its ancestors.
        // Remove all transforms from ancestors to recover fixed positioning.
        &.transform-none {
          transform: none;
        }

        // fixed viewport blocker, that will auto close toggle standards&frameworks
        .question-subtitle-background-blocker {
          position: fixed;
          z-index: 1;
          top: 0px;
          left: 0px;
          right: 0px;
          bottom: 0px;
        }
        .toggle-button {
          width: 1.8rem;
          height: 1.8rem;
          padding: 0rem;
          color: var(--theme-TextPlaceholder);
          &:hover,
          &.active {
            background-color: var(--theme-AccentMedium);
            i {
              color: var(--theme-TextWhite);
            }
          }
        }
        .ctlight-toggle-button {
          width: 2rem;
          height: 2rem;
          padding: 0rem;
          margin-left: 0.25rem;
          color: var(--theme-TextPlaceholder);
          &:hover {
            background-color: var(--theme-AccentMedium);
            i {
              color: var(--theme-TextWhite);
            }
          }
        }
      }

      .standards-frameworks-container {
        z-index: 1;
        background-color: var(--theme-TextWhite);
        border: 1px solid var(--theme-NeutralsLight);
        border-radius: 8px;
        padding: 0.5rem;
        box-shadow: 4px 4px rgba(var(--theme-TextPlaceholder), 0.5);
      }

      .ctlight-standards-frameworks-container {
        background-color: var(--theme-TextWhite);
        overflow: hidden;
        width: calc(#{$icon-size} + 6px); // account for 3px button padding
        transition: all 0.3s ease-in-out;
        padding: 1px 0px;
        .standards-frameworks-toolbar .header {
          transition: all 0.3s ease-in-out;
          visibility: hidden;
          transform-origin: left;
          transform: scaleX(0.6);
        }
        &.expand {
          width: 100%;
          overflow: visible;
          .standards-frameworks-toolbar .header {
            visibility: visible;
            transform: scaleX(1);
          }
        }
      }

      .status-info {
        background-color: var(--theme-ColourWhite) !important;

        &.verified {
          color: var(--theme-SuccessLight);
          border: 1px solid var(--theme-SuccessLight);
        }
        &.created {
          color: var(--theme-TextMedium);
          border: 1px solid var(--theme-TextMedium);
        }
        &.rejected {
          color: var(--theme-DangerMedium);
          border: 1px solid var(--theme-DangerMedium);
        }
        &.updated {
          color: var(--theme-AccentMedium);
          border: 1px solid var(--theme-AccentMedium);
        }
      }

      .na-nr-info {
        font-weight: 400;
        text-align: center;
        .na-nr-info-top {
          border-radius: 1rem;
          color: var(--theme-TextMedium);
          border: 1px solid var(--theme-TextMedium);
          background-color: var(--theme-TextWhite);
          font-size: 9px;
          line-height: 13.5px;
          padding: 1px 10px;
        }
        .na-nr-info-bottom {
          padding: 0.3rem 0.1rem;
          width: 7rem;
          color: var(--theme-TextPlaceholder);
          font-size: 0.6rem;
        }
      }
    }

    .button-container {
      button {
        min-width: 120px;
      }
    }

    .instructions {
      text-decoration: underline;
    }

    .text-faded {
      opacity: 0.5;
    }

    .form-confirmation-input {
      label {
        font-size: 1rem;
        color: var(--theme-AccentExtradark);
        font-weight: bold;
      }

      .input-group {
        border: 1px solid var(--theme-BgDisabled);
        background-color: var(--theme-TextWhite) !important;
        border-radius: 4px;
        font-size: 13px;
        text-align: left;

        i {
          color: var(--theme-AccentMedium);
        }

        .input-group-text {
          padding-right: 3px;
        }

        input,
        button,
        .input-group-text {
          margin: 1px 0px;
          font-size: 13px;
          border: none;
          background-color: var(--theme-TextWhite) !important;
        }

        input::placeholder {
          color: var(--theme-TextPlaceholder) !important;
        }

        button {
          margin-right: 1px;
          border-radius: $borderRadius;
          border-left: 1px solid var(--theme-BgDisabled);
          background-color: var(--theme-AccentMedium) !important;
          color: var(--theme-TextWhite) !important;

          &.disabled {
            background-color: var(--theme-TextWhite) !important;
            color: var(--theme-TextPlaceholder) !important;
          }

          &:hover {
            background-color: var(--theme-AccentExtradark) !important;
            color: var(--theme-TextWhite) !important;
            text-decoration: none;
          }
        }
      }
    }

    .post-submit-flash {
      transition: background-color 0.5s ease-in-out;
      
      &.updated {
        background-color: $colorThemeSuccessExtralight;
      }

      &.rejected {
        background-color: $colorThemeDangerMedium;
      }
    }
  }

  .evidence-tooltip-icon {
    color: var(--theme-TextPlaceholder);
  }

  &.has-sidebar-left {
    @include media-breakpoint-up(lg) {
      animation-fill-mode: forwards;
      animation-name: animated-translate;
      animation-duration: .8s;
    }
    // Very large screen has enough space for sidebar and question view already
    @media (min-width: 1800px) {
      animation: none;
    }
  }
}

@keyframes animated-translate {
  0% {
    margin-left: 15%;
  }
  100% {
    margin-left: $sidebar-width-lg;
  }
}
