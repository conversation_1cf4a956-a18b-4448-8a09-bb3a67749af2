/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { Button } from 'reactstrap';
import { UpdateActions } from '../../../actions/universalTrackerValue';
import { getAnalytics } from '../../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../../services/analytics/AnalyticsEvents';
import UniversalTracker from '../../../model/UniversalTracker';
import { TOOLTIP } from '../../../constants/labels';
import { SurveyModelMinimalUtrv } from '../../../model/surveyData';
import { isNa, isNr, canExecuteAction, isCompletedOpenOrRestated } from '../../../utils/universalTrackerValue';
import {
  getSubmitAndVerifyButtonTooltip,
  getSubmitButtonTooltip,
  getVerifyButtonTooltip,
  canSubmitAndVerifyAction,
  getRejectButtonTooltip,
} from './toolbar/utils/action-buttons';
import { SubmitVerifiedButtonState } from './constants';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { DraftButton } from './draft/DraftButton';
import classNames from 'classnames';

const { REJECT, VERIFY, UPDATE } = UpdateActions.STATUS;
const analytics = getAnalytics();

const getPayload = (utrv: Pick<SurveyModelMinimalUtrv, '_id' | 'initiativeId'>, utr: UniversalTracker) => ({
  utrvId: utrv._id,
  utrId: utr.getId(),
  utrCode: utr.getCode(),
  initiativeId: utrv.initiativeId,
  utrValueLabel: utr.getValueLabel(),
});

export const RejectButton = (props: QuestionSubmitButtonsProps) => {
  const { utrv, utr, disabled, handleSubmitQuestion, hasValidComments } = props;
  const canBeRejected = canExecuteAction(utrv, REJECT);
  const isDisabled = disabled || !canBeRejected || !hasValidComments;

  return (
    <SimpleTooltip text={getRejectButtonTooltip(isDisabled)}>
      <Button
        color='link'
        outline={true}
        className='ml-2 reject-btn'
        disabled={isDisabled}
        onClick={() => {
          analytics.track(AnalyticsEvents.SurveyReject, getPayload(utrv, utr));
          handleSubmitQuestion(REJECT);
        }}
        data-testid='question-reject-btn'
      >
        Reject submission
      </Button>
    </SimpleTooltip>
  );
}

export const VerifyButton = (props: QuestionSubmitButtonsProps) => {
  const { utrv, utr, disabled, hasAnythingChanged, handleSubmitQuestion } = props;

  const canBeVerified = canExecuteAction(utrv, VERIFY);
  const isDisabled = hasAnythingChanged || disabled || !canBeVerified;

  return (
    <SimpleTooltip text={getVerifyButtonTooltip(isDisabled)}>
      <Button
        color='primary'
        className='ml-2'
        disabled={isDisabled}
        onClick={() => {
          analytics.track(AnalyticsEvents.SurveyVerify, getPayload(utrv, utr));
          handleSubmitQuestion(VERIFY);
        }}
        data-testid='question-verify-btn'
      >
        Verify
      </Button>
    </SimpleTooltip>
  );
}

export const SubmitAndVerifyButton = (props: QuestionSubmitButtonsProps) => {
  const { utrv, utr, disabled, hasAnythingChanged, formValid, handleSubmitUpdate, hasValidComments, isVerificationRequired } = props;

  const isVerified = utrv.status === UpdateActions.ACTION.UTR.VERIFIED;
  const isRestating = Boolean(isCompletedOpenOrRestated(utrv.assuranceStatus));
  const isDisabled =
    !formValid ||
    disabled ||
    (isVerified && !hasAnythingChanged) ||
    (isRestating && !hasValidComments);

  const getBtnTitle = () => {
    if (isVerificationRequired) {
      return isRestating ? (
        'Restate as verified'
      ) : (
        <span>
          <i className='fa-light fa-check mr-2' />
          Submit as verified
        </span>
      );
    }

    return isRestating ? (
      'Restate'
    ) : (
      <span>
        <i className='fa-light fa-check mr-2' />
        Submit
      </span>
    );
  };

  return (
    <SimpleTooltip text={getSubmitAndVerifyButtonTooltip(utrv, isDisabled)}>
      <Button
        color='primary'
        className='ml-2'
        disabled={isDisabled}
        onClick={(e) => {
          analytics.track(AnalyticsEvents.SurveyConfirmVerify, getPayload(utrv, utr));
          handleSubmitUpdate(e, true);
        }}
        data-testid='question-submit-verify-btn'
      >
        {getBtnTitle()}
      </Button>
    </SimpleTooltip>
  );
};

export const SubmitButton = (props: QuestionSubmitButtonsProps) => {
  const {
    utrv,
    utr,
    disabled,
    hasAnythingChanged,
    hasValidComments,
    formValid,
    handleSubmitUpdate,
    isVerifier,
    isContributor,
    isVerificationRequired
  } = props;
  const isContributorAndVerifier = isContributor && isVerifier;
  const canBeUpdated = canExecuteAction(utrv, UPDATE, isVerifier) || !isVerificationRequired;
  const isContributorUpdatingRejectedUtrv = isContributor && utrv.status === UpdateActions.ACTION.UTR.REJECTED;
  const isDisabled =
    disabled ||
    !formValid ||
    !(canBeUpdated && ((hasAnythingChanged && hasValidComments) || isContributorUpdatingRejectedUtrv));

  const btnTitle = isCompletedOpenOrRestated(utrv.assuranceStatus) ? (
    'Restate'
  ) : (
    <span>
      <i className={`fa-light mr-2 ${isContributorAndVerifier ? 'fa-pencil' : 'fa-check'}`} />
      Submit
    </span>
  );

  return (
    <SimpleTooltip id='submit' text={getSubmitButtonTooltip(utrv, isDisabled, isContributorAndVerifier)}>
      <Button
        color='primary'
        outline={isContributorAndVerifier}
        className='ml-2'
        disabled={isDisabled}
        onClick={(e) => {
          analytics.track(AnalyticsEvents.SurveyConfirm, getPayload(utrv, utr));
          handleSubmitUpdate(e, false);
        }}
        data-testid='question-submit-btn'
      >
        {btnTitle}
      </Button>
    </SimpleTooltip>
  );
}

export const NotReportingButton = (props: QuestionSubmitButtonsProps) => {
  const {
    utrv,
    disabled,
    isVerifier,
    isVerificationRequired,
    handleNotReporting,
  } = props;

  if (!handleNotReporting) {
    return <></>;
  }

  const isVerified = utrv.status === UpdateActions.ACTION.UTR.VERIFIED;
  const isVerificationLocked = isVerified && isVerificationRequired && !isVerifier;

  return (
    <SimpleTooltip text={TOOLTIP.notReporting}>
      <Button
        color='warning'
        outline={true}
        disabled={disabled || isVerificationLocked}
        onClick={handleNotReporting}
        data-testid='question-not-reporting-btn'
      >
        <i className='fa-light fa-pencil-slash mr-2' />
        Not reporting
      </Button>
    </SimpleTooltip>
  );
};

interface PrivacyButtonProps {
  utrv: Pick<SurveyModelMinimalUtrv, '_id' | 'isPrivate'>;
  disabled: boolean;
  handlePrivacy: null | (() => void);
}
export const PrivacyButton = (props: PrivacyButtonProps) => {
  const { utrv, disabled, handlePrivacy } = props;

  if (!handlePrivacy) {
    return <></>;
  }

  const isPrivate = utrv.isPrivate;

  return (
    <SimpleTooltip text={TOOLTIP.privacy}>
      <Button
        className='ms-2'
        color='transparent'
        disabled={disabled}
        onClick={handlePrivacy}
        data-testid='question-privacy-btn'
      >
        {isPrivate ? (
          <span>
            <i className='fa-light fa-eye mr-2' />
            Make public
          </span>
        ) : (
          <span>
            <i className='fa-light fa-eye-slash mr-2' />
            Make private
          </span>
        )}
      </Button>
    </SimpleTooltip>
  );
};

export interface QuestionSubmitButtonsProps {
  utrv: Pick<SurveyModelMinimalUtrv, '_id' | 'initiativeId' | 'status' | 'value' | 'valueData' | 'isPrivate' | 'assuranceStatus'>;
  utr: UniversalTracker;
  isContributor: boolean;
  isVerifier: boolean;
  disabled: boolean;
  hasAnythingChanged: boolean;
  hasValidComments: boolean;
  formValid: boolean;
  handleSubmitUpdate: (e: React.MouseEvent, verify?: boolean) => void;
  handleSubmitQuestion: (action: string, notApplicableType?: string, autoVerify?: boolean) => void;
  isVerificationRequired: boolean;
  isDraft?: boolean;
  handleNotReporting: null | (() => void);
}

/*
    unlocked question always has verified status
    hence, stakeholder only cannot update unlocked question (as it is still verified)
    verifier only cannot verify unlocked question (as it is still verified)
    only user has both roles can choose to update / update & verify unlocked question
    reject unlocked question requires note as well
    check canExecuteAction()
  */

const QuestionSubmitButtons = (props: QuestionSubmitButtonsProps) => {
  const { utrv, isContributor, isVerifier, isVerificationRequired, hasAnythingChanged } = props;
  const canBeVerified = canSubmitAndVerifyAction(utrv, SubmitVerifiedButtonState.Show);
  const canBeRejected = canExecuteAction(utrv, REJECT);

  const isUpdated = utrv.status === UpdateActions.ACTION.UTR.UPDATED;
  const alreadySubmitted = isUpdated && !hasAnythingChanged;

  const showRejectButton = isVerifier && isVerificationRequired && canBeRejected;
  const showVerifiedButton = isVerifier && isVerificationRequired && canBeVerified;
  const showSubmitButton = isContributor && isVerificationRequired;

  const contributorAutoVerify = !isVerificationRequired;
  const verifierSubmit = isVerificationRequired && isVerifier && !alreadySubmitted;
  const showSubmitVerifyButton = isContributor && (contributorAutoVerify || verifierSubmit);

  if (props.isDraft) {
    // Draft mode does not support submit right now
    return (
      <div className='row'>
        <div className='col-12 text-right'>
          <DraftButton {...props} />
        </div>
      </div>
    );
  }

  return (
    <div className='row'>
      <div className='col-4'>{isContributor ? <NotReportingButton {...props} /> : null}</div>
      <div className='col-8 text-right'>
        {showRejectButton ? <RejectButton {...props} /> : null}
        {showSubmitButton ? <SubmitButton {...props} /> : null}
        {showSubmitVerifyButton ? (
          <SubmitAndVerifyButton {...props} />
        ) : showVerifiedButton ? (
          <VerifyButton {...props} />
        ) : null}
      </div>
    </div>
  );
}

export default QuestionSubmitButtons;
