/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import { SurveyActionData } from '../../../model/surveyData';
import { useHistory, useLocation } from 'react-router-dom';
import UniversalTracker from '../../../model/UniversalTracker';

interface BreadcrumbsProps {
  utr?: UniversalTracker;
  survey: SurveyActionData;
  selectedAltCode: string;
  rootBreadcrumb: {
    label: string;
    url: string;
  }
}

export const QuestionContainerHeaderBreadcrumbs = (props: BreadcrumbsProps) => {
  const { utr, selectedAltCode, rootBreadcrumb } = props;

  const history = useHistory();
  const location = useLocation();

  if (!utr) {
    return null;
  }

  const goToOverview = () => {
    const searchParams = new URLSearchParams(location.search);
    searchParams.delete('standard'); // Don't carry the standard around
    history.push({
      pathname: rootBreadcrumb.url,
      search: searchParams.toString(),
    })
  }

  const standardCode = utr.getTypeCode(selectedAltCode);
  const standardName = utr.getTypeName(selectedAltCode);

  const divider = () => {
    return <div className='mx-2'>&#47;</div>
  }

  return <div className='question-container-breadcrumbs ml-2 d-flex flex-row text-ThemeTextPlaceholder'>
    <Button className='d-inline-flex p-0' color='link' onClick={goToOverview} data-testid='survey-overview-breadcrumb-item'>
      {rootBreadcrumb.label}
    </Button>
    {divider()}
    <div>{`${standardName} ${utr && standardCode ? '- ' + standardCode : ''} `}</div>
  </div>
}

