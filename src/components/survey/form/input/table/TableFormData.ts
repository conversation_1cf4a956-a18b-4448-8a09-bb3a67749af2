/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { getTableConfiguration } from '../../../../../utils/universalTracker'
import { UnitTypes } from '../../../../../utils/units';
import { getConvertedValue } from '../../../../../utils/universalTrackerValue';
import { InputColumn, TableColumn, ValueTable } from './InputInterface';
import { TableDataInfo } from '../../../question/questionInterfaces';

interface ConversionParams {
  value?: any;
  unit?: string;
  numberScale?: string;
}

const createConversionParams = (column: TableColumn, { value, unit, numberScale }: ConversionParams) => {

  return {
    unit,
    numberScale,
    value,
    defaultUnit: column.unit,
    defaultNumberScale: column.numberScale,
    isCurrency: column.unitType === UnitTypes.currency,
  }
}

type ColumnMap = Map<string, TableColumn>;

const convertInputData = (rowData: InputColumn[][], mapCode: ColumnMap): InputColumn[][] => {
  return rowData.map(row => row.map((col) => {
    const colMetadata = mapCode.get(col.code);
    if (colMetadata) {
      const params = createConversionParams(colMetadata, col);
      return {
        ...col,
        numberScale: params.defaultNumberScale,
        unit: params.isCurrency ? col.unit : params.defaultUnit,
        value: getConvertedValue(params)
      }
    }
    return col;
  }));
};

interface TableSubmitValues {
  submitValues: any;
  table?: TableDataInfo;
  universalTracker: any;
}

export const updateTableDataSubmitValues = (submitData: TableSubmitValues) => {

  const { submitValues, table, universalTracker } = submitData;

  const tableConfig: ValueTable | undefined = getTableConfiguration(universalTracker);

  if (!tableConfig || !table || !Array.isArray(table.rows)) {
    return submitValues;
  }

  const { columns } = tableConfig;
  const mapCode: ColumnMap = new Map(columns.map(c => [c.code, c]));

  const rowData = table.rows.filter(r => !r.isRemoved).map(r => r.data);

  submitValues.valueData.input.table = rowData.map((rowColumns) => {
    return rowColumns.map((col) => {
      const colMetadata = mapCode.get(col.code);
      if (!colMetadata) {
        return col;
      }
      const { isCurrency, defaultUnit, defaultNumberScale } = createConversionParams(colMetadata, col);
      const unit = isCurrency ? undefined : col.unit ?? defaultUnit;
      return {
        ...col,
        numberScale: col.numberScale ?? defaultNumberScale,
        unit,
      };
    });
  });

  submitValues.valueData.table = convertInputData(rowData, mapCode);
  return submitValues;
}
