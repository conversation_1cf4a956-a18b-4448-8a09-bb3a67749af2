/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { Input } from 'reactstrap';
import { ColumnInputProps } from '../InputInterface';
import { getSingleRowTableAddon } from '../tableUtils';

export default function DateInput(props: ColumnInputProps) {
  const {
    label,
    isDisabled,
    universalTracker,
    column,
    inputColumn,
    handleFocus,
    updateColumn,
    placeholder
  } = props;

  const disabled = isDisabled?.(props);

  const value = inputColumn.value;
  const { beforeAddon, afterAddon } = getSingleRowTableAddon({ ...props, columnCode: column.code });

  const validValue = typeof value === 'string' || typeof value === 'undefined';
  if (!validValue) {
    return <>Unsupported data for date input</>;
  }

  return (
    <div className='mb-3'>
      {label ?? null}
      <div className='position-relative w-100'>
        {beforeAddon?.element || null}
        <Input
          type='date'
          autoComplete='off'
          className='styled-input'
          name={universalTracker.getId()}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          required={true}
          onFocus={handleFocus}
          onClick={handleFocus}
          onChange={(e) =>
            updateColumn({
              code: column.code,
              value: e.target.value,
            })
          }
        />
        {afterAddon?.element || null}
      </div>
    </div>
  );
}
