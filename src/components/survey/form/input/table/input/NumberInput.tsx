/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { Input, InputGroup } from 'reactstrap';
import { ColumnInputProps, TableColumn } from '../InputInterface';
import {
  InputTableGroupPrePend,
  TableInputGroupAppend,
} from '../TableInputGroup';
import ValueSlider from '../../ValueSlider';
import { hasMinAndMaxColumnType } from '../../../../../../types/universalTracker';
import { HandleValueChangeProps } from '../../InputProps';
import { InputHelperText } from '../../InputHelperText';
import { getUtrDecimal, getUtrNumberValue } from '@utils/universalTracker';
import { getSingleRowTableAddon } from '../tableUtils';
import { MappedConnectionWrapper, usePopulateInputFromConnection } from '@features/assistant';
import classnames from 'classnames';
import { isDefined } from '@utils/index';
import { getTableInputColumnWarningMessage } from '@components/survey/utils/input';
import { useVariationContext } from '@components/survey/question/variation/VariationContext';

const getValidatedValue = (value: string, column: TableColumn) => {
  if (!column.validation) {
    return value;
  }

  const numericValue = Number(value);

  const { max, min } = column.validation;
  if (max !== undefined && numericValue > max) {
    return max;
  }

  if (min !== undefined && numericValue < min) {
    return min;
  }

  return value;
};

export default function NumberInput(props: ColumnInputProps) {
  const {
    handleFocus,
    updateColumn,
    label,
    column,
    inputColumn,
    isDisabled,
    placeholder,
    inputMessage,
    universalTracker,
    hasValueChanged,
    initiativeUtr,
  } = props;

  const { beforeAddon, afterAddon } = getSingleRowTableAddon({ ...props, columnCode: column.code });

  const updateValue = (name: string, value: string) => updateColumn({
    code: name,
    value: value === '' ? undefined : getValidatedValue(value, column)
  });

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    const { name, value } = e.target;
    return updateValue(name, value);
  };

  const value = inputColumn.value ?? '';

  const { required = true, max, min } = column.validation ?? {};
  // 0-100 treat as percentage slider
  const renderSlider = hasMinAndMaxColumnType(column.type, min, max);
  const disabled = isDisabled?.(props);
  const invalidMessage = inputMessage?.[column.code];
  const isInvalid = !!invalidMessage;

  const decimal = getUtrDecimal(universalTracker, inputColumn.code);
  const valueWithDecimal = getUtrNumberValue({ value: String(value), decimal, hasValueChanged });
  const unitWarningMessage = getTableInputColumnWarningMessage({
    initiativeUtr,
    inputColumn,
  });

  const { getVariationWarningMessage } = useVariationContext();
  const warningMessage = unitWarningMessage || getVariationWarningMessage(column.code);

  usePopulateInputFromConnection({
    inputChangeHandler: ({ value }) => {
      updateColumn({ value, code: column.code });
    },
    valueListCode: column.code,
  });

  if (renderSlider) {
    return (
      <MappedConnectionWrapper valueListCode={column.code}>
        <div className='mb-3'>
          {label}
          <div className='position-relative w-100'>
            {beforeAddon?.element || null}
            <ValueSlider
              value={valueWithDecimal}
              universalTracker={universalTracker}
              disabled={disabled}
              placeholder={placeholder}
              handleChange={({ value }: HandleValueChangeProps) => updateValue(column.code, value)}
              invalidMessage={invalidMessage}
              column={column}
              min={min}
              max={max}
              suffix={'%'}
              numberScale={inputColumn.numberScale ?? column.numberScale}
              handleNumberScaleChange={(numberScale: string) => updateColumn({ code: column.code, numberScale })}
              status={props.status}
              initiativeUtr={props.initiativeUtr}
              warningMessage={warningMessage}
            />
            {afterAddon?.element || null}
          </div>
        </div>
      </MappedConnectionWrapper>
    );
  }

  return (
    <MappedConnectionWrapper valueListCode={column.code}>
      <div className='mb-3'>
        {label}
        <InputGroup
          className={classnames('position-relative w-100', { 'has-validation': isDefined(decimal) || warningMessage })}
        >
          {beforeAddon?.element || null}
          {InputTableGroupPrePend({ ...props, isInvalid })}
          <Input
            data-testid={column.code}
            type='number'
            max={max}
            min={min}
            placeholder={placeholder}
            autoComplete='off'
            className={isInvalid ? '' : 'styled-input'}
            id={column.code}
            name={column.code}
            value={valueWithDecimal}
            aria-describedby='suffix'
            disabled={!!disabled}
            required={required}
            onFocus={handleFocus}
            onClick={handleFocus}
            invalid={isInvalid}
            onWheel={(e) => e.currentTarget.blur()}
            onChange={onChange}
          />
          {TableInputGroupAppend({ ...props, isInvalid })}
          {disabled ? null : (
            <InputHelperText
              invalidMessage={invalidMessage}
              universalTracker={universalTracker}
              columnCode={column.code}
              warningMessage={warningMessage}
            />
          )}
          {afterAddon?.element || null}
        </InputGroup>
      </div>
    </MappedConnectionWrapper>
  );
}
