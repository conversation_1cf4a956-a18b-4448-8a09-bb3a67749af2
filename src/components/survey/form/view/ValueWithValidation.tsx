/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { ValidationItem } from '@components/survey/question/questionInterfaces';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface ValueWithValidationParams {
  rowId: number;
  columnCode: string;
  value: string,
  validation: ValidationItem[]
}


// Convert validation type to icon name using font awesome
const iconMap: { [key: string]: string | undefined } = {
  error: 'fa fa-exclamation-triangle text-ThemeDangerMedium',
  warning: 'fa fa-exclamation-circle text-ThemeWarningMedium',
  info: 'fa fa-check-circle',
  unknown: 'fa fa-question-circle',
};

export function ValueWithValidation(props: ValueWithValidationParams) {

  if (!props.validation.length) {
    return props.value;
  }

  // Aggregated by the validation type
  const validationMap = props.validation.reduce((acc, item) => {
    if (!acc[item.type]) {
      acc[item.type] = [];
    }
    acc[item.type].push(item);
    return acc;
  }, {} as Record<string, ValidationItem[]>);

  // Render icon with tooltip messages

  return (
    <>
      <span data-testid={`row-validation-${props.rowId}-${props.columnCode}`}>
        {Object.keys(validationMap).map((key) => {
          const messages = (
            <ul>
              {validationMap[key].map((item, index) => (
                <li key={`${key}-${index}`}>{item.value}</li>
              ))}
            </ul>
          );
          const icon = iconMap[key] ?? iconMap.unknown;
          return (
            <SimpleTooltip className={'me-1'} key={key} text={messages}>
              <i className={icon} />
            </SimpleTooltip>
          );
        })}
      </span>

      {props.value}
    </>
  )
}
