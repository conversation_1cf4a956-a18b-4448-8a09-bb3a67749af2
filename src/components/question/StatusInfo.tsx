/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React from 'react';
import { UtrvStatus } from '../../constants/status';
import './StatusInfo.scss';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface Props {
  status: string,
  statusText: string
}

export const StatusInfo = ({ status, statusText }: Props) => {
  if (status === UtrvStatus.Created) {
    return null;
  }

  return <div className='status-info-container'>
    <SimpleTooltip text={statusText}>
      <span className={`status-info ${status}`}>{status}</span>
    </SimpleTooltip>
  </div>;
};
