/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import React from 'react';
import { NotApplicableTypes } from '../../constants/status';
import './NotApplicableBadge.scss';
import { Badge } from 'reactstrap';

interface Props {
  notReported?: string;
  showExplanation?: boolean;
}

export const NotApplicableBadge = ({ notReported, showExplanation = true }: Props) => {

  if (!notReported || ![NotApplicableTypes.na, NotApplicableTypes.nr].includes(notReported)) {
    return null;
  }

  const isNr = NotApplicableTypes.nr === notReported;
  return (
    <div className='ml-2 d-inline-block na-nr-info'>
      <Badge className='fw-normal background-ThemeBgExtralight text-ThemeTextMedium border border-ThemeTextMedium px-2 py-1' pill>
        {isNr ? 'Not reporting' : 'Not applicable'}
      </Badge>
      {showExplanation ?
        <div className='na-nr-info-bottom text-center'>
          {isNr ? 'enter an answer to start reporting' : 'enter an answer to make applicable'}
        </div>
        : <></>
      }
    </div>
  )
}
