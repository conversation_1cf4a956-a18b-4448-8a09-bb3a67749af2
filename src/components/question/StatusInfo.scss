/*!
 * Copyright (c) 2021. World Wide Generation Ltd
 */

@import '../../css/variables';
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins';

.status-info-container {
  .status-info {
    text-align: center;
    color: var(--theme-TextWhite);
    cursor: pointer;

    &.verified {
      background-color: var(--theme-SuccessMedium);
    }
    &.created {
      background-color: var(--theme-TextPlaceholder);
    }
    &.rejected {
      background-color: var(--theme-DangerMedium);
    }
    &.updated {
      background-color: var(--theme-AccentMedium);
    }
  }
}
