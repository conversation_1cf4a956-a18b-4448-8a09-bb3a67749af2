/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { useContext } from 'react';
import { getCurrentUser, isUserManagerByInitiativeId } from '../../selectors/user';
import { getQuestionProgress } from '../../selectors/survey';
import { loadSurvey, unloadSurveyListSummary } from '../../actions/survey';
import { reloadSurveyList } from '../../slice/initiativeSurveyListSlice';
import { reloadInitiative } from '../../actions/initiative';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { SurveyContext, SurveyContextLoadedProps } from './SurveyContainer';
import { CompleteButton } from '../survey/button/CompleteButton';
import './style.scss';
import SurveyProgress, { AnimationVariant } from '../survey-progress';
import { getRootOrg } from '@selectors/initiative';
import { AppCode } from '@g17eco/types/app';
import { AIAutoAnswer } from '@features/ai-auto-answer';

export default function SurveyOverviewHeader() {
  const { surveyData } = useContext(SurveyContext) as SurveyContextLoadedProps;
  const dispatch = useAppDispatch();

  const progressStats = useAppSelector(getQuestionProgress);
  const isUserAdmin = useAppSelector((state) => {
    if (!state.survey.loaded) {
      return false;
    }
    const currentUser = getCurrentUser(state);
    if (currentUser && state.survey.data.roles?.admin?.includes(currentUser._id)) {
      return true;
    }
    return isUserManagerByInitiativeId(state, state.survey.data.initiativeId);
  });
  const rootInitiative = useAppSelector(getRootOrg);

  const handleReload = async (): Promise<void> => {
    if (surveyData._id) {
      await Promise.all([
        dispatch(loadSurvey(surveyData._id, true, true)),
        dispatch(unloadSurveyListSummary()),
        dispatch(reloadSurveyList()),
        dispatch(reloadInitiative())
      ]);
    }
  }

  return (
    <div className='w-100 d-flex'>
      <div className='flex-grow-1 pr-3'>
        <AIAutoAnswer
          survey={surveyData}
          initiativeId={surveyData.initiativeId}
          progressStats={progressStats}
        />
        <SurveyProgress {...progressStats} animationVariant={AnimationVariant.Animated} />
        <div className='text-sm text-right text-ThemeTextMedium mt-1'>
          <span>answered <span className='dont_translate'>{progressStats.completed}/{progressStats.count}</span></span>
          <span className='px-3'>|</span>
          <span>verified <span className='dont_translate'>{progressStats.verified}/{progressStats.count}</span></span>
        </div>
      </div>
      <div className='flex-grow-0'>
        <CompleteButton
          survey={surveyData}
          isUserAdmin={isUserAdmin}
          handleReload={handleReload}
          classes={{ icon: 'fs-3 ' }}
          appConfigCode={rootInitiative?.appConfigCode as AppCode | undefined}
        />
      </div>
    </div>
  );
}
