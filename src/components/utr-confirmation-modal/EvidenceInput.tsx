/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { FileDropZone } from '../files/FileDropZone'
import {
  InputGroup,
  InputGroupText,
  Input,
  Button
} from 'reactstrap'
import { FileListContainer, FileListContainerProps } from '../files/FileListContainer'
import React from 'react'
import { nl2br } from '../../utils';
import { SupportingEvidenceTooltip } from './SupportingEvidenceTooltip';
import { CollapseButton, CollapseContent, CollapsePanel } from '@g17eco/molecules/collapse-panel';
import { EvidenceLink } from '../../types/universalTracker';
import { HandleFileDescriptionFn } from '../../types/file';
import { QUESTION } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { useToggle } from '@hooks/useToggle';
import { EvidenceModal } from './EvidenceModal';
import { EvidenceFile } from '@components/survey/question/questionInterfaces';
import { useAppSelector } from '@reducers/index';
import { isStaff } from '@selectors/user';

function displayEvidenceInstructions(evidenceInstructions: string) {
  if (!evidenceInstructions) {
    return '';
  }

  const tooltip = <div className='text-left'>{nl2br(evidenceInstructions)}</div>;

  return <SimpleTooltip placement={'top'} text={tooltip}>
    <Button color='link' size='sm'>
      <u>Suggested Evidence</u>
    </Button>
  </SimpleTooltip>
}

interface EvidenceProps extends Omit<FileListContainerProps, 'metadata'> {
  disabled: boolean;
  handleEvidenceLinkAdd: (link: any, isPublic: boolean) => void;
  handleFileDescriptionAdd: HandleFileDescriptionFn;
  handleFilesAdded: (addFiles: File[]) => void;
  isEvidenceRequired?: boolean;
  evidenceInstructions: string;
  isContributor: boolean;
  isVerifier: boolean;
  initiativeId: string;
  setFiles: (addFiles: EvidenceFile[]) => void;
}

const defaultEvidenceLink: EvidenceLink = { url: '', description: '' };

export const EvidenceInput = (props: EvidenceProps) => {
  const [openDocumentsModal, toggleDocumentsModal] = useToggle(false);
  const [publicEvidenceLink, setPublicLink] = React.useState<EvidenceLink>(defaultEvidenceLink);
  const [privateEvidenceLink, setPrivateLink] = React.useState<EvidenceLink>(defaultEvidenceLink);
  const isUserStaff = useAppSelector(isStaff);

  const {
    disabled,
    handleEvidenceLinkAdd,
    handleFilesAdded,
    isEvidenceRequired,
    evidenceInstructions,
    isContributor,
    isVerifier,
    existingFiles,
    files,
    initiativeId,
    setFiles,
  } = props;

  const hasAction = isContributor || isVerifier;
  const hasExistingFiles = existingFiles && existingFiles.length > 0;

  const updateLink = (value: EvidenceLink, isPublic: boolean) => {
    handleEvidenceLinkAdd(value, isPublic);
    if (isPublic) {
      setPublicLink(defaultEvidenceLink);
    } else {
      setPrivateLink(defaultEvidenceLink);
    }
  }

  const hasData = files.length > 0 || (existingFiles && existingFiles.length > 0);

  return (
    <CollapsePanel className='py-3 border-top border-bottom' collapsed={!isEvidenceRequired && !hasExistingFiles}>
      <CollapseButton classes={{ content: 'd-block' }}>
        <h5 className='d-flex align-items-center gap-3 text-ThemeAccentExtradark'>
          Supporting Evidence {isEvidenceRequired ? '*' : ''}
          <div className='evidence-tooltip-icon'>
            <SupportingEvidenceTooltip />
          </div>
        </h5>
      </CollapseButton>
      <CollapseContent>
        <div className='form-confirmation-input pt-3'>
          <h2 className={'title-container'}>
            <span></span>
            <span className='text-right'>{displayEvidenceInstructions(evidenceInstructions)}</span>
          </h2>
          {hasAction && !disabled ? (
            <div>
              {isUserStaff ? (
                <div className='d-flex mb-3 gap-1 justify-content-center'>
                  <Button color='primary' outline size='lg' onClick={toggleDocumentsModal}>
                    <i className='fal fa-folders mr-2' />
                    Search Document Library
                  </Button>
                  <EvidenceModal
                    key={files.length}
                    handleFilesAdded={setFiles}
                    files={files}
                    existingFiles={existingFiles}
                    initiativeId={initiativeId}
                    isOpen={openDocumentsModal}
                    toggle={toggleDocumentsModal}
                  />
                </div>
              ) : null}
              <FileDropZone disabled={disabled} multiple={true} onDrop={handleFilesAdded} />

              <div className='d-flex mt-3 gap-1 align-items-center'>
                <InputGroup>
                  <InputGroupText>
                    <i className='fa fa-link' />
                  </InputGroupText>
                  <Input
                    key='input-evidence-link'
                    type='text'
                    disabled={disabled}
                    placeholder='Add a link to EXTERNAL files'
                    value={publicEvidenceLink.url}
                    onChange={(e) => setPublicLink({ ...publicEvidenceLink, url: e.target.value })}
                  />
                </InputGroup>
                <InputGroup style={{ width: '35%' }}>
                  <Input
                    key='evidence-link-description'
                    type='text'
                    disabled={disabled}
                    placeholder='Page reference if applicable'
                    value={publicEvidenceLink.description}
                    onChange={(e) => setPublicLink({ ...publicEvidenceLink, description: e.target.value })}
                  />
                </InputGroup>
                <Button
                  color='primary'
                  disabled={!publicEvidenceLink.url}
                  onClick={() => updateLink(publicEvidenceLink, true)}
                >
                  Add
                </Button>
              </div>

              <div className='d-flex mt-3 gap-1 align-items-center'>
                <InputGroup>
                  <InputGroupText>
                    <i className='fa fa-lock' />
                  </InputGroupText>
                  <Input
                    key='input-evidence-link'
                    type='text'
                    disabled={disabled}
                    placeholder='Add a reference or link to INTERNAL files'
                    value={privateEvidenceLink.url}
                    onChange={(e) => setPrivateLink({ ...privateEvidenceLink, url: e.target.value })}
                  />
                </InputGroup>
                <InputGroup style={{ width: '35%' }}>
                  <Input
                    key='evidence-link-description'
                    type='text'
                    disabled={disabled}
                    placeholder='Page reference if applicable'
                    value={privateEvidenceLink.description}
                    onChange={(e) => setPrivateLink({ ...privateEvidenceLink, description: e.target.value })}
                  />
                </InputGroup>
                <Button
                  color='primary'
                  disabled={!privateEvidenceLink.url}
                  onClick={() => updateLink(privateEvidenceLink, false)}
                >
                  Add
                </Button>
              </div>
            </div>
          ) : null}

          {!hasAction && !hasData ? (
            <BasicAlert type='info'>There are no evidence files for this {QUESTION.SINGULAR}</BasicAlert>
          ) : (
            <FileListContainer
              title={'Evidence to be added'}
              {...props}
              allowToggle={props.isContributor}
              metadata={{ initiativeId }}
            />
          )}
        </div>
      </CollapseContent>
    </CollapsePanel>
  );
}
