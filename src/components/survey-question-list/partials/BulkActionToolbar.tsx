/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useContext, useMemo, useState } from 'react';
import { loadSurvey, reloadSurveyListSummary } from '../../../actions/survey';
import { saveUniversalTrackerValue, UpdateActions } from '../../../actions/universalTrackerValue';
import { TOOLTIP } from '../../../constants/labels';
import { NotApplicableTypes } from '../../../constants/status';
import { SurveyActionData } from '../../../model/surveyData';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { getCurrentUser } from '../../../selectors/user';
import { AnalyticsEvents } from '../../../services/analytics/AnalyticsEvents';
import { getAnalytics } from '../../../services/analytics/AnalyticsService';
import G17Client, { FlagProperties } from '../../../services/G17Client';
import { SurveyPermissions } from '../../../services/permissions/SurveyPermissions';
import { addSiteAlert, SiteAlertColors } from '../../../slice/siteAlertsSlice';
import { UniversalTrackerValuePlain } from '../../../types/surveyScope';
import { SubmitQuestionData } from '../../../utils/files';
import DelegationModal from '../../delegation-modal';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { useToggle } from '../../../hooks/useToggle';
import { BulkButton, countQuestions } from './ToggledButton';
import { ActionsMenu } from './ActionsMenu';
import './BulkActionToolbar.scss';
import { FeaturePermissions } from '../../../services/permissions/FeaturePermissions';
import { UniversalTrackerBlueprintMin } from '../../../types/universalTracker';
import { CongratulationsModal } from '../../complete-survey-modal/CongratulationsModal';
import { useToggleCompleteSurveys } from '../../../hooks/useToggleCompleteSurveys';
import { useToggleCongratulationsModal } from '../../complete-survey-modal/useToggleCongratulationsModal';
import { ConfigurationType, InputOverrideType, QuestionConfigurationDropdown, QuestionConfigurationModal } from '@features/question-configuration';
import { QUESTION } from '@constants/terminology';
import { InitiativeUniversalTracker } from '@g17eco/types/initiativeUniversalTracker';
import { FloatingToolbar } from '@components/floating-toolbar';
import { QuestionManagementContext } from '@apps/company-tracker/components/admin-dashboard/questions/QuestionManagementContainer';
import { SurveyFloatingToolbarButton } from '@utils/survey';
import { useChatbotVisibilityControl } from '@hooks/useChatbotVisibilityControl';
import { NotReportingModal, omissionMap, OmissionReason } from '@components/survey/question/NotReportingModal';
import { Option } from '@g17eco/molecules/select/SelectFactory';
import { isNa, isNr } from '@utils/universalTrackerValue';

export type BulkActionUtrMin = Partial<Pick<UniversalTrackerBlueprintMin, 'name' | 'valueValidation' | 'unit' | 'unitType' | 'valueType' | 'numberScale'>>;

export type BulkActionToolbarSelectedUtrv = Pick<
  UniversalTrackerValuePlain,
  | '_id'
  | 'evidenceRequired'
  | 'verificationRequired'
  | 'noteRequired'
  | 'isPrivate'
  | 'status'
  | 'assuranceStatus'
  | 'stakeholders'
  | 'initiativeId'
  | 'universalTrackerId'
  | 'value'
  | 'valueData'
> & { utr?: BulkActionUtrMin };

export type BulkActionUtr = Pick<UniversalTrackerBlueprintMin, '_id'> & BulkActionUtrMin;

export interface BulkActionToolbarProps {
  surveyId: string;
  initiativeId: string;
  selectedQuestions: BulkActionToolbarSelectedUtrv[];
  triggerBulkAction: (properties: FlagProperties) => void;
  toggleSelectAll: () => void;
  handleClose: () => void;
  questionCount: number;
  handleReload?: () => void;
  hiddenOptions?: (ConfigurationType | SurveyFloatingToolbarButton)[];
  surveyData: Pick<
    SurveyActionData,
    '_id' | 'roles' | 'stakeholders' | 'initiativeId' | 'fragmentUniversalTrackerValues'
  >;
  rootInitiativeUtrMap?: Map<string, InitiativeUniversalTracker>;
}

export const BulkActionToolbar = (props: BulkActionToolbarProps) => {
  const {
    selectedQuestions,
    handleClose,
    surveyId,
    surveyData,
    initiativeId,
    hiddenOptions = [],
    rootInitiativeUtrMap,
  } = props;
  const dispatch = useAppDispatch();
  const canAccessVerification = useAppSelector(FeaturePermissions.canAccessVerification);

  const [configType, setConfigType] = useState<ConfigurationType | undefined>();
  const [delegationModal, setDelegationModal] = useState<BulkActionToolbarSelectedUtrv[]>([]);
  const [openActionsMenu, toggleActionsMenu, setOpenActionsMenu] = useToggle(false);
  const [isLoading, setLoading] = useState(false);
  const currentUser = useAppSelector(getCurrentUser);
  const { setInputOverrideType, setUtrvConfigCode } = useContext(QuestionManagementContext);

  const [openNotReportingModal, toggleNotReportingModal, setNotReportingModal] = useToggle(false);
  const [comments, setComments] = useState<string>();

  const canContribute = currentUser && SurveyPermissions.canContribute(surveyData, currentUser);

  const selectedCount = useMemo(() => countQuestions(selectedQuestions), [selectedQuestions]);

  useChatbotVisibilityControl({ isHidden: selectedCount > 0 });

  const convertedSelectedQuestions = useMemo(
    () =>
      selectedQuestions.map((utrv) => {
        const { universalTrackerId, utr = {} } = utrv;
        const { name, valueType, valueValidation, unit, unitType, numberScale } = utr;
        return { _id: universalTrackerId, name, valueType, valueValidation, unit, unitType, numberScale };
      }),
    [selectedQuestions]
  );

  const handleReload = async () => {
    return Promise.all([
      dispatch(loadSurvey(surveyId, false, true)),
      dispatch(reloadSurveyListSummary()),
    ])
      .then(() => props.handleReload?.());
  }

  const { toggleCompleteSurveys } = useToggleCompleteSurveys({
    handleReload,
    surveys: [surveyData],
    isCompleting: true,
  });

  const { isCongratulationsModalOpen, openCongratulationsModal, toggleCongratulationsModal } =
    useToggleCongratulationsModal({ survey: surveyData, currentUser, toggleCompleteSurveys });

  if (!currentUser) {
    return null;
  }

  const handleCatch = (e: string) => {
    setLoading(false);
    dispatch(addSiteAlert({
      content: `Could not update ${QUESTION.PLURAL}`,
      color: SiteAlertColors.Danger
    }));
  }

  const reloadOnClosingModal = async ({ reloadSurvey = false }: { reloadSurvey?: boolean } = {}) => {
    if (!reloadSurvey) {
      return;
    }
    setLoading(true);
    await handleReload();
    setLoading(false);
  };

  function handleBulkFlagChange(properties: FlagProperties, utrvIds: string[]) {
    if (!canContribute || !surveyId) {
      return;
    }
    setLoading(true);
    return G17Client.updateSurveyQuestionFlags({ surveyId, utrvIds, properties })
      .then(() => {
        getAnalytics().track(AnalyticsEvents.SurveyBulkAction, {
          utrvIds,
          properties,
          surveyId,
          initiativeId
        });
        props.triggerBulkAction(properties);
        return handleReload();
      })
      .then(() => {
        setLoading(false);
        setOpenActionsMenu(false);
      })
      .catch(handleCatch)
  }

  const handleBulkNAByType = (naType: string) => {
    if (!canContribute) {
      return;
    }
    const handleSubmitQuestion = (utrvId: string, notApplicableType: string) => {
      const data: SubmitQuestionData = {
        autoVerify: 'true',
        valueData: { notApplicableType, input: {} },
        note: comments
      };
      return dispatch(saveUniversalTrackerValue(UpdateActions.STATUS.UPDATE, utrvId, data)) as unknown as Promise<unknown>;
    }

    setLoading(true);
    setNotReportingModal(false);
    Promise.all(selectedQuestions.map(selectedQuestion => handleSubmitQuestion(selectedQuestion._id, naType)))
      .then(() => handleReload())
      .then(() => {
        openCongratulationsModal({ editingUtrvIds: selectedQuestions.map(({_id}) => _id), verified: true })
      })
      .then(() => setLoading(false))
      .catch(handleCatch)
  }

  function downloadCurrentQuestionCsv() {
    if (surveyId) {
      const ids = selectedQuestions.map(u => u._id)
      return G17Client.downloadSurveyQuestions(surveyId, ids);
    }
  }

  const canManage = surveyData && SurveyPermissions.canManage(surveyData, currentUser);

  const toggledButtonCommon = {
    rootInitiativeUtrMap,
    selectedQuestions,
    handleBulkFlagChange,
    canManage,
  }

  const modalProps = {
    handleReload: reloadOnClosingModal,
    selectedQuestions: convertedSelectedQuestions,
    initiativeId,
  };

  const notReportingButton = !hiddenOptions.includes(SurveyFloatingToolbarButton.NotReporting) && canContribute ? (
    <BulkButton className='' tooltip={TOOLTIP.notReporting} onClick={toggleNotReportingModal}>
      Not reporting
    </BulkButton>
  ) : null;

  const downloadButton = (
    <BulkButton className='d-flex gap-2'
      tooltip='Download'
      onClick={downloadCurrentQuestionCsv}
    >
      <i className='fal fa-file-csv text-ThemeIconSecondary' />
      Download
    </BulkButton>
  );

  const delegateButton = canManage ? (
    <BulkButton className='d-flex gap-2' tooltip='Delegate' onClick={() => setDelegationModal(selectedQuestions)}>
      <i className='fal fa-users-medical text-ThemeIconSecondary' />
      Delegate
    </BulkButton>
  ) : null;

  const reportSetting = canManage ? (
    <ActionsMenu
      {...toggledButtonCommon}
      openMenu={openActionsMenu}
      toggleMenu={toggleActionsMenu}
      canAccessVerification={canAccessVerification}
    />
  ) : null

  const allConfigurationTypeAreHidden = Object.values(ConfigurationType).every((option) =>
    hiddenOptions.includes(option)
  );

  const isMultipleQuestionSelected = modalProps.selectedQuestions.length > 1

  const platformMetricOverridesButton =
    hiddenOptions.includes(SurveyFloatingToolbarButton.Platform) || allConfigurationTypeAreHidden ? null : (
      <>
        <QuestionConfigurationDropdown
          changeConfigType={(configType) => {
            setConfigType(configType);
            setInputOverrideType(isMultipleQuestionSelected ? InputOverrideType.NumberScale : undefined);
            setUtrvConfigCode(isMultipleQuestionSelected ? 'verificationRequired' : undefined);
          }}
          dropdownToggle={
            <div>
              <i className='fa-light fa-square-question fs-5 me-2'></i> Platform {QUESTION.SINGULAR} overrides
            </div>
          }
          hiddenOptions={
            hiddenOptions.filter((op: any) => Object.values(ConfigurationType).includes(op)) as ConfigurationType[]
          }
          {...modalProps}
        />
        {configType ? (
          <QuestionConfigurationModal
            isOpen={true}
            toggleOpen={() => {
              setConfigType(undefined);
            }}
            configType={configType}
            {...modalProps}
          />
        ) : null}
      </>
    );

  const selectedCountLabel = (
    <span className='p-1 text-colorThemeTextMedium d-inline-block' style={{ minWidth: '80px' }}>
      {selectedCount} selected
    </span>
  );

  const isAllNaQuestions = selectedQuestions.every((question) => isNa(question));
  const isAllNrQuestions = selectedQuestions.every((question) => isNr(question));
  const omissionOptions: Option<OmissionReason>[] = [
    {
      label: omissionMap.na.label,
      value: omissionMap.na.value,
      isDisabled: isAllNaQuestions,
    },
    ...[omissionMap.nrProhibitions, omissionMap.nrConstraints, omissionMap.nrUnavailable].map(({ label, value }) => ({
      label,
      value,
      isDisabled: isAllNrQuestions,
    })),
  ];

  return (
    <>
      {isLoading ? <BlockingLoader /> : null}
      <FloatingToolbar
        handleClose={handleClose}
        items={[
          notReportingButton,
          reportSetting,
          platformMetricOverridesButton,
          delegateButton,
          downloadButton,
          selectedCountLabel,
        ]}
        isOpen={selectedCount > 0}
      />

      {delegationModal.length > 0 ? (
        <DelegationModal
          isOpen={delegationModal.length > 0}
          survey={surveyData}
          handleSubmit={async () => {
            setLoading(true);
            await handleReload();
            setLoading(false);
          }}
          toggle={() => setDelegationModal([])}
          initiativeId={initiativeId}
          utrvs={delegationModal}
        />
      ) : null}
      <CongratulationsModal isOpen={isCongratulationsModalOpen} toggle={toggleCongratulationsModal} />
      <NotReportingModal
        key={openNotReportingModal ? 'open-not-reporting' : 'close-not-reporting'}
        isOpen={openNotReportingModal}
        toggle={() => setNotReportingModal(false)}
        options={omissionOptions}
        comments={comments}
        submitNA={() => handleBulkNAByType(NotApplicableTypes.na)}
        submitNR={() => handleBulkNAByType(NotApplicableTypes.nr)}
        handleComments={(value) => setComments(value)}
      />
    </>
  );
};
