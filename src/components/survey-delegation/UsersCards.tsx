/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { CardGridGroup, CardGridItemProps } from '../survey-scope/CardGrid';
import {
  CardProps,
  ScopeQuestionOptionalValue
} from '../../types/surveyScope';
import { SurveyUserRoles, UserMin } from '@constants/users';
import { roles } from '../../constants/roles';
import { ViewValues } from '../survey-overview-sidebar/viewOptions';
import { StakeholderGroup } from '../../model/stakeholderGroup';
import { QUESTION } from '@constants/terminology';

export const getUsersCards = (props: CardProps) => {
  const { questionList, addBtn, handleDrilldown, breadcrumbs, users, surveyStakeholders, handleToggleDelegation, isUserDelegated } = props;

  const cardGroups: CardGridGroup[] = [];
  const delegatedCards: CardGridItemProps[] = [];
  const undelegatedCards: CardGridItemProps[] = [];
  const isSurveyLevelScope = !breadcrumbs?.[0] || breadcrumbs[0].cardGroup === 'users';
  const userRole = breadcrumbs?.[breadcrumbs.length - 1]?.cardCategory as SurveyUserRoles;

  const filterUserByQuestion = (user: UserMin, userRole: SurveyUserRoles, question: ScopeQuestionOptionalValue) => {
    if (userRole === SurveyUserRoles.Verifier) {
      return question?.utrv?.stakeholders?.verifier.includes(user._id)
    }
    if (userRole === SurveyUserRoles.Stakeholder) {
      return question?.utrv?.stakeholders?.stakeholder.includes(user._id)
    }
    return false;
  }

  const isSurveyStakeholder = (user: UserMin, type: SurveyUserRoles) => {
    const userId = user._id;
    if ([SurveyUserRoles.Admin, SurveyUserRoles.Monitor].includes(type)) {
      return !!user.roles?.includes(type);
    }
    return surveyStakeholders?.[type as keyof StakeholderGroup]?.includes(userId);
  };

  const getDescription = (user: UserMin, userRole: SurveyUserRoles) => {
    const isSurveyLevelStakeholder = isSurveyStakeholder(user, userRole);
    const roleDetails = roles[userRole];
    const roleIcon = <i className={`${roleDetails.icon}`} />;
    const roleName = roleDetails.shortName ?? roleDetails.name;
    const key = `card-${user._id}-${userRole}`;

    if (isSurveyLevelStakeholder) {
      const text = <strong>all</strong>;
      const button = handleToggleDelegation ? <a className='toggle-link' onClick={(e: React.MouseEvent<any, MouseEvent>) => handleToggleDelegation(e, userRole)} href='# ' rel='noopener noreferrer'>{text}</a> : text;
      return <p key={key} >{roleIcon}{roleName} for {button} {QUESTION.PLURAL}</p>;
    }
    const unitCount = getQuestionCount(user, userRole);
    if (unitCount === 0) {
      return undefined;
    }

    return <p key={key}>{roleIcon}{roleName} for <strong>{unitCount}</strong> {QUESTION.PLURAL}</p>;
  }

  const getQuestionCount = (user: UserMin, userRole: SurveyUserRoles) => {
    const filteredUserList = questionList.filter(question => filterUserByQuestion(user, userRole, question));
    return filteredUserList.length;
  }

  users.forEach((user) => {
    const buttons = [];
    const username = user.firstName ?? '';
    const surname = user.surname ?? '';
    const name = `${username} ${surname}`;
    const userNotOnboarded = !user._id && 'email' in user;
    const renderProfile = () => {
      if (user.profile) {
        return <img src={user.profile} alt={'user profile'} width='40px' />
      } else {
        return <i className='fas fa-user text-ThemeTextWhite background-ThemeNeutralsLight'></i>
      }
    }

    let additionalInfo = undefined;
    let unitCount: number | undefined = undefined;
    let unitName = '';
    let isDelegated = isUserDelegated ? isUserDelegated(user._id) : false;
    let canAddUser = false;

    const roleUserDrilldown = breadcrumbs[breadcrumbs.length - 1]?.cardGroup === ViewValues.Users
      && breadcrumbs[breadcrumbs.length - 2]?.cardGroup === ViewValues.Roles
    switch (userRole) {
      case SurveyUserRoles.Stakeholder:
      case SurveyUserRoles.Verifier: {
        const isSurveyLevelRole = isSurveyStakeholder(user, userRole);
        if (isSurveyLevelRole) {
          isDelegated = true;
          additionalInfo = getDescription(user, userRole);
          canAddUser = isSurveyLevelScope;
        } else {
          const questionCount = getQuestionCount(user, userRole);
          if (questionCount > 0) {
            unitName = `${QUESTION.CAPITALIZED_PLURAL} Delegated`;
            unitCount = questionCount;
          } else {
            additionalInfo = <></>;
          }
          canAddUser = roleUserDrilldown;
        }
        break;
      }
      case SurveyUserRoles.Admin:
      case SurveyUserRoles.Monitor:
        additionalInfo = getDescription(user, userRole);
        canAddUser = isSurveyLevelScope && ['roles', 'users'].includes(breadcrumbs?.[0]?.cardGroup);
        break;
      default: {
        const additionalInfoOptions = [
          getDescription(user, SurveyUserRoles.Admin),
          getDescription(user, SurveyUserRoles.Stakeholder),
          getDescription(user, SurveyUserRoles.Verifier),
          getDescription(user, SurveyUserRoles.Monitor),
        ].filter(el => el !== undefined);
        additionalInfo = <>{additionalInfoOptions}</>;
        break;
      }
    }

    if (!userNotOnboarded) {
      if (isDelegated) {
        buttons.push({
          tooltip: `View ${QUESTION.PLURAL} in this scope`,
          icon: <i className='fa fa-list text-ThemeTextPlaceholder' />,
          onClick: () => handleDrilldown(ViewValues.QuestionList, user._id, '')
        });
      }
      if (canAddUser) {
        buttons.push(addBtn(user._id, name));
      }
    }

    const card = {
      key: `scope-cardgriditem-standards-${user._id}`,
      title: <><span>{name}</span></>,
      sortTitle: name,
      subtitle: user.jobTitle ?? '',
      displayGridSubtitles: true,
      footer: user.company ?? '',
      icon: renderProfile(),
      unitCount: unitCount,
      unitName: unitName,
      buttons: buttons,
      additionalInfo: additionalInfo,
      scopeTag: '',
      inScope: false,
      isPartial: false
    };

    if (userNotOnboarded) {
      const name = user.firstName || user.email;
      card.key = `scope-cardgriditem-standards-${name}`;
      card.title = <span className='dont_translate'>{name}</span>;
      card.subtitle = 'Invitation sent';
      card.additionalInfo = <p>This user has not completed onboarding and is not available for delegation</p>;
    }

    if (isDelegated || (!userRole && !userNotOnboarded)) {
      delegatedCards.push({ ...card, inScope: true });
    } else {
      undelegatedCards.push({ ...card, inScope: false });
    }
  });

  if (delegatedCards.length > 0) {
    cardGroups.push({
      cards: delegatedCards
    });
  }
  if (undelegatedCards.length > 0) {
    cardGroups.push({
      name: 'Unassigned users',
      cards: undelegatedCards
    });
  }
  return cardGroups;
}
