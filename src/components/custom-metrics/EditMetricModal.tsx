/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON>, <PERSON><PERSON><PERSON>ooter, ModalHeader } from 'reactstrap';
import type { InputType } from 'reactstrap/types/lib/Input';
import {
  addMetricToGroup,
  createMetric,
  createMetricGroup,
  deleteMetric,
  updateMetric,
} from '../../actions/initiative';
import { SDGGoal, sdgMap, SDGTarget } from '../../constants/sdg-data';
import { useAppDispatch } from '../../reducers';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { Alternative, LanguageAlternative, UniversalTrackerPlain, UtrValueType, ValueValidation } from '@g17eco/types/universalTracker';
import { FormGenerator, Validation } from '@g17eco/molecules/form';
import { Loader } from '@g17eco/atoms/loader';
import SDGPicker from '../sdg-picker';
import { CustomMetricContext } from './CustomMetricContainer';
import LanguageFormContainer from './partials/LanguageFormContainer';
import { FormProps } from './MetricGroupForm';
import { canSetNumberScale, canSetUnitType, hasInvalidAlternativeFields, hasMinAndMax, hasPassedValidation, isValidMinMaxData, validateTypeCode } from './utils';
import { QUESTION, SURVEY } from '@constants/terminology';
import { ValueTypeSettingsContainer } from './partials/ValueTypeSettingsContainer';
import { useCheckIsCustomMetricAnsweredQuery } from '@api/custom-metrics';
import { CANNOT_EDIT_NOTE } from './constants';
import classNames from 'classnames';
import { Option } from '@g17eco/molecules/select/SelectFactory';

export interface Form {
  _id?: string;
  name: string;
  valueLabel: string;
  typeCode?: string;
  instructions?: string;
  valueType: string;
  unitType?: string;
  numberScale?: string;
  tags: { sdg: string[] };
  alternatives?: {
    [key: string]: Alternative | LanguageAlternative;
  };
  valueValidation: {
    min?: number;
    max?: number;
  }
  submitting: boolean;
  errored?: boolean;
  errorMessage: string;
}

export interface Field {
  code: keyof Form;
  type: InputType;
  required?: boolean;
  disabled?: boolean;
  label: string;
  placeholder?: string;
  options?: Option<string>[];
  classes?: {
    label: string;
  }
  tooltip?: string;
  isValid?: (v: Form) => Validation;
  note?: string;
  testId?: string;
}

const getGroupAFields = (): Field[] => [
  {
    code: 'name',
    type: 'text' as InputType,
    required: true,
    label: `${QUESTION.CAPITALIZED_SINGULAR} title`,
    placeholder: `e,g. Supplier living wage - (this is what appears on the ${SURVEY.CAPITALIZED_SINGULAR} overview page)`,
    classes: {
      label: 'fw-bold',
    },
    testId: 'custom-metric-name-input'
  },
  {
    code: 'valueLabel',
    type: 'text' as InputType,
    required: true,
    label: `${QUESTION.CAPITALIZED_SINGULAR} text`,
    placeholder: `e,g. What is your supplier living wage? - (this is text of the actual ${QUESTION.SINGULAR})`,
    classes: {
      label: 'fw-bold',
    },
    testId: 'custom-metric-valueLabel-input'
  },
  {
    code: 'typeCode',
    type: 'text' as InputType,
    required: false,
    label: `${QUESTION.CAPITALIZED_SINGULAR} code (max 15 characters)`,
    tooltip: 'Max 15 characters, and cannot include spaces',
    placeholder: 'e,g. pack1-q3',
    classes: {
      label: 'fw-bold',
    },
    isValid: validateTypeCode,
    testId: 'custom-metric-typeCode-input'
  },
  {
    code: 'instructions',
    type: 'textarea' as InputType,
    label: `${QUESTION.CAPITALIZED_SINGULAR} instructions`,
    placeholder: `If there are any special instructions or tips on answering this ${QUESTION.SINGULAR}, you can add them here`,
    classes: {
      label: 'fw-bold',
    },
    testId: 'custom-metric-instructions-input'
  },
];

export const getInitialState: () => Form = () => ({
  errorMessage: '',
  name: '',
  valueLabel: '',
  typeCode: '',
  valueType: '',
  tags: {
    sdg: [],
  },
  valueValidation: {},
  submitting: false,
});

const getGroupBFields = (isAnswered: boolean): Field[] => [
  {
    code: 'valueType',
    type: 'select' as InputType,
    required: true,
    disabled: isAnswered,
    label: 'Value Type',
    options: [
      { value: 'text', label: 'Text' },
      { value: 'number', label: 'Number' },
      { value: 'percentage', label: 'Percentage' },
      { value: 'valueList', label: 'Yes/No' },
      { value: 'date', label: 'Date' },
    ],
    classes: {
      label: 'fw-bold',
    },
    note: isAnswered ? CANNOT_EDIT_NOTE : undefined,
    testId: 'metric-value-type-select'
  }
];

export interface EditMetricModalProps extends Omit<FormProps, 'customMetricsUsage'> {
  isOpen: boolean;
  editUtrId?: string;
  questions: UniversalTrackerPlain[];
  isAddingToGroup?: boolean;
  isReadOnly?: boolean;
}

export const EditMetricModal = ({
  isOpen,
  editUtrId,
  questions,
  handleReload,
  metricGroup,
  handleCancel = () => { },
  isAddingToGroup = false,
  isReadOnly = false,
}: EditMetricModalProps) => {

  const dispatch = useAppDispatch();
  const [isSaving, setSaving] = React.useState(false);
  const [errorMessage, setMessage] = React.useState('');
  const { initiativeId } = React.useContext(CustomMetricContext);
  const [formData, setFormData] = useState<Form>(getInitialState());

  const { data: { isAnswered } = { isAnswered: true } } = useCheckIsCustomMetricAnsweredQuery(
    { initiativeId, utrId: editUtrId ?? '' },
    { skip: !initiativeId || !editUtrId }
  );

  const isEditMode = !!formData._id;
  const question = questions.find((q) => q._id === editUtrId);

  const groupAFields = getGroupAFields();
  const groupBFields = useMemo(() => getGroupBFields(isAnswered && isEditMode), [isAnswered, isEditMode]);
  const combinedMetricFields = [...groupAFields, ...groupBFields];
  const isValid = (() => {
    const hasEmptyRequiredField = combinedMetricFields.some(
      (field) => field.required && !formData[field.code]
    );
    if (hasEmptyRequiredField) {
      return false;
    }
    const hasInvalidAlternatives = hasInvalidAlternativeFields({
      formFields: groupAFields,
      alternatives: formData.alternatives,
    });
    const passedValidation = hasPassedValidation({ formFields: groupAFields, formData });
    const hasValidMinMax = isValidMinMaxData(formData.valueValidation);

    return !hasInvalidAlternatives && passedValidation && hasValidMinMax;
  })();

  // Fields to display in form
  const groupBFilteredFields = !isEditMode
    ? groupBFields
    : groupBFields.filter((f) => {
      if (f.code === 'valueType') {
        // Check if we should display valueType, only for currently supported types
        return f.options?.some((option) => option.value === formData.valueType);
      }
      return true;
    });

  const onChange = (e: React.ChangeEvent<{ name: string; value: any }>) => {
    const name = e.target.name;
    const value = e.target.value;
    const newFormData = { ...formData, errorMessage: '', [name]: value };
    if (!canSetUnitType(newFormData)) {
      newFormData.unitType = '';
    }

    if (!canSetNumberScale(newFormData)) {
      newFormData.numberScale = '';
    }

    setFormData((prev) => ({ ...prev, ...newFormData }));
  };

  const setSDGCodes = (codes: string[]) => {
    const newFormData: any = {
      ...formData,
      errorMessage: '',
      tags: {
        sdg: codes,
      },
    };

    setFormData(newFormData);
  };

  const setValueValidation = (value: ValueValidation) => {
    const hasMinAndMaxValue = hasMinAndMax(value);
    setFormData((prev) => ({
      ...prev,
      numberScale: hasMinAndMaxValue ? '' : prev.numberScale,
      valueValidation: {
        ...prev.valueValidation,
        ...value,
      },
    }));
  };

  const handleCancelMetric = () => {
    setSaving(false);
    setFormData(getInitialState());
    handleCancel();
  };

  const handleDeleteMetric = async (utrId: string) => {
    const actionConfirmation = window.confirm(
      'Are you sure you want to delete this Metric? All data associated with Metric will be lost and cannot be recovered.'
    );
    if (!actionConfirmation) {
      return;
    }

    setSaving(true);
    if (utrId) {
      try {
        await deleteMetric(initiativeId, utrId);
        dispatch(
          addSiteAlert({
            content: (
              <>
                <strong>Success!</strong> Custom metric has been removed
              </>
            ),
            color: SiteAlertColors.Primary,
            timeout: 3000,
          })
        );
        handleReload();
        handleCancelMetric();
      } catch (e) {
        setMessage('There was an error deleting this Metric. If this continues please contact our support');
      }
    }
    setSaving(false);
  };

  const handleCreateOrUpdateMetric = async () => {
    const utrData: any = {};
    [
      ...combinedMetricFields.map((k) => k.code),
      'tags',
      'alternatives',
      'valueValidation',
      'unitType',
      'numberScale',
    ].forEach((code) => {
      utrData[code] = formData[code as keyof Form];
    });

    setSaving(true);
    try {
      if (isEditMode) {
        await updateMetric(initiativeId, formData._id, utrData);
        handleReload();
        handleCancelMetric();
        return;
      }

      const utr = await createMetric(initiativeId, utrData);

      if (!isAddingToGroup) {
        return;
      }

      let groupId;
      if (metricGroup?._id) {
        groupId = metricGroup._id;
      } else {
        const groupData = await createMetricGroup(initiativeId, metricGroup);
        groupId = groupData._id;
      }

      const analytics = getAnalytics();
      await analytics.track(AnalyticsEvents.CustomMetricCreated, {
        initiativeId,
        utrId: utr._id,
        utrCode: utr.code,
        utrValueLabel: utr.valueLabel,
        groupId: groupId,
      });

      await addMetricToGroup(initiativeId, groupId, utr._id);
      dispatch(
        addSiteAlert({
          content: (
            <>
              <strong>Success!</strong> Custom metric has been created
            </>
          ),
          color: SiteAlertColors.Primary,
          timeout: 3000,
        })
      );
    } catch (e) {
      setMessage('There was an error saving this Metric. If this continues please contact our support.');
    } finally {
      handleReload();
      handleCancelMetric();
    }
  };

  React.useEffect(() => {
    if (formData.valueType === UtrValueType.Percentage && !formData._id) {
      setValueValidation({
        min: 0,
        max: 100,
      });
    }
  }, [formData._id, formData.valueType]);

  React.useEffect(() => {
    if (!question) {
      return;
    }

    setFormData((f) => {
      const data: Form = {
        ...f,
        _id: question._id,
        name: question.name ?? '',
        valueLabel: question.valueLabel ?? '',
        instructions: question.instructions ?? '',
        typeCode: question.typeCode ?? '',
        valueType: question.valueType ?? '',
        unitType: question.unitType ?? '',
        numberScale: question.numberScale ?? '',
        alternatives: question.alternatives,
        valueValidation: question.valueValidation ?? {},
        tags: {
          sdg: [],
        },
      };

      const sdg = question?.tags?.sdg;
      if (sdg) {
        sdgMap.forEach((goal: SDGGoal) => {
          if (sdg?.includes(goal.code)) {
            data.tags.sdg.push(goal.code);
            goal.targets.forEach((target: SDGTarget) => {
              if (!target.hidden && sdg?.includes(target.code)) {
                data.tags.sdg.push(target.code);
              }
            });
          }
        });
      }
      return data;
    });
  }, [editUtrId, question, questions]);

  return (
    <Modal isOpen={isOpen} toggle={handleCancelMetric} backdrop='static' size='lg'>
      <ModalHeader toggle={handleCancelMetric}>Create a custom {QUESTION.SINGULAR}</ModalHeader>
      <ModalBody className={classNames({ 'pe-none': isReadOnly })}>
        <div className='position-relative'>
          {isSaving && <Loader />}
          <div className={isSaving ? 'isSaving' : ''}>
            {errorMessage && <div className='alert alert-danger'>{errorMessage}</div>}
            <FormGenerator fields={groupAFields} form={formData} updateForm={onChange} />
            {isReadOnly ? null : <LanguageFormContainer formData={formData} onChange={onChange} />}
            <FormGenerator fields={groupBFilteredFields} form={formData} updateForm={onChange} />
            <ValueTypeSettingsContainer
              initialData={question}
              formData={formData}
              onChange={onChange}
              setValueValidation={setValueValidation}
              isAnswered={isAnswered && isEditMode}
            />
            <SDGPicker
              handleUpdate={setSDGCodes}
              selectedCodes={formData.tags?.sdg ?? []}
              includeTargets={true}
              classes={{ label: 'fw-bold' }}
            />
          </div>
        </div>
      </ModalBody>
      <ModalFooter className='pt-0'>
        <div className='text-left mr-auto'>
          <Button
            disabled={!isEditMode || isAnswered || isReadOnly}
            color='danger'
            className='mr-2'
            onClick={() => handleDeleteMetric(formData?._id ?? '')}
          >
            Delete
          </Button>
        </div>
        <div className='text-right'>
          <Button color='link-secondary' onClick={() => handleCancelMetric()} className='mr-3'>
            Cancel
          </Button>
          <Button disabled={!isValid || isReadOnly} color='primary' className='ml-3' onClick={handleCreateOrUpdateMetric} data-testid='custom-metric-form-submit-btn'>
            {isEditMode ? 'Update' : 'Create'}
          </Button>
        </div>
      </ModalFooter>
    </Modal>
  );
};
