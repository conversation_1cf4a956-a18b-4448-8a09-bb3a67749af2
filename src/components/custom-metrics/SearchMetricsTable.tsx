/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useCallback, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import type { InputType } from 'reactstrap/types/lib/Input';
import { useAppSelector } from '../../reducers';
import { generateUrl } from '../../routes/util';
import { getInitiativeBlueprintFlexSearchMap } from '../../selectors/blueprint';
import { getCurrentUser } from '../../selectors/user';
import { UniversalTrackerPlain } from '../../types/universalTracker';
import { InitiativeTree } from '../../utils/initiative';
import { FormGenerator } from '@g17eco/molecules/form';
import { ViewMode } from './constants';
import { CustomMetricContext, getBaseRoute } from './CustomMetricContainer';
import { FormProps } from './MetricGroupForm';
import { MetricTable } from './MetricTable';
import './styles.scss';
import { QUESTION } from '@constants/terminology';
import { hasHitQuestionLimit } from './utils';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

export interface MetricFormProps extends FormProps {
  questions: UniversalTrackerPlain[];
  handleCreateNewMetric: () => void;
}

const searchFields = [
  {
    code: 'name',
    type: 'text' as InputType,
    required: false,
    label: `Search for an existing ${QUESTION.SINGULAR}`,
    testId: 'search-metrics-input'
  },
];

const SearchMetricsTable = (props: MetricFormProps) => {
  const {
    metricGroup,
    handleReload,
    questions,
    customMetricsUsage,
    handleCreateNewMetric,
  } = props;

  const history = useHistory();
  const { initiativeId, isPortfolioTracker, surveyId } = React.useContext(CustomMetricContext);

  const [searchText, setSearchText] = useState('');
  const [filteredQuestions, setFilteredQuestions] = useState<UniversalTrackerPlain[]>([]);
  const index = useAppSelector(getInitiativeBlueprintFlexSearchMap);
  const throttleTimeout = useRef<ReturnType<typeof setTimeout> | undefined>();
  const user = useAppSelector(getCurrentUser);

  const handleOpenQuestionForm = () => {
    const baseRoute = getBaseRoute(isPortfolioTracker);
    const params = {
      initiativeId,
      portfolioId: initiativeId,
      groupId: metricGroup?._id,
      view: ViewMode.EditQuestion,
    };
    const query = surveyId ? `?surveyId=${surveyId}` : '';
    history.push(`${generateUrl(baseRoute, params)}${query}`);
    handleCreateNewMetric();
  };

  const onChange = (e: React.ChangeEvent<any>) => {
    setSearchText(e.target.value);
    search(e.target.value);
  };

  const search = useCallback((searchText: string) => {
    if (throttleTimeout.current) {
      clearTimeout(throttleTimeout.current);
    }

    const filterAssignedMetrics = (question: Pick<UniversalTrackerPlain, 'ownerId'>) => {
      if (!question.ownerId) {
        return true;
      }
      return user && InitiativeTree.isDescendant(user, initiativeId, question.ownerId);
    }

    throttleTimeout.current = setTimeout(() => {
      if (!searchText) {
        return setFilteredQuestions([]);
      }

      const results = index.search(searchText);
      const matchedIds = new Set(results.map((question) => question.result).flat());
      setFilteredQuestions(
        questions
          .filter((question) => matchedIds.has(question._id))
          .filter(filterAssignedMetrics)
      );
    }, 300);
  }, [user, initiativeId, questions, index, setFilteredQuestions]);

  const isHitQuestionLimit = hasHitQuestionLimit(customMetricsUsage);

  return (
    <div className='position-relative mt-3'>
      <div className='d-flex align-items-end'>
        <div className='col-sm-6 col-12'>
          <FormGenerator fields={searchFields} form={{ name: searchText }} updateForm={onChange} />
        </div>
        <div className='col-sm-6 col-12'>
          <div className='d-flex justify-content-end align-items-center mb-3'>
            <div>Can’t find your {QUESTION.SINGULAR}:</div>
            <SimpleTooltip
              text={isHitQuestionLimit ? 'Limit reached. Please upgrade if more than custom metrics are needed' : ''}
            >
              <Button color={'secondary'} outline disabled={isHitQuestionLimit} className='ml-3' onClick={handleOpenQuestionForm} data-testid='create-custom-metric-btn'>
                Create a custom {QUESTION.SINGULAR}
              </Button>
            </SimpleTooltip>
          </div>
        </div>
      </div>
      <div className='mt-3'>
        <MetricTable
          questions={filteredQuestions}
          metricGroup={metricGroup}
          handleReload={handleReload}
          helperText={`Search for a ${QUESTION.SINGULAR} to assign...`}
        />
      </div>
    </div>
  );
};
export default SearchMetricsTable;
