/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { addMetricToGroup, createMetricGroup, removeMetricFromGroup } from '../../actions/initiative';
import { UniversalTrackerPlain } from '../../types/universalTracker';
import { getGroup, standards } from '@g17eco/core';
import { Loader } from '@g17eco/atoms/loader';
import { NameCell } from './partials/NameCell';
import './styles.scss';
import { addSiteAlert, SiteAlertColors } from '../../slice/siteAlertsSlice';
import { getAnalytics } from '../../services/analytics/AnalyticsService';
import { AnalyticsEvents } from '../../services/analytics/AnalyticsEvents';
import IconButton from '../button/IconButton';
import { FormProps } from './MetricGroupForm';
import { CustomMetricContext } from './CustomMetricContainer';
import { getCurrentUser } from '../../selectors/user';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { InitiativeTree } from '../../utils/initiative';
import { QUESTION } from '@constants/terminology';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { MetricDetail } from './partials/MetricDetail';
import { getBadge, isUTRInGroup } from './utils';

export interface MetricTableProps extends Omit<FormProps, 'customMetricsUsage'> {
  questions: UniversalTrackerPlain[];
  helperText?: string;
}

export const MetricTable = ({ metricGroup, handleReload, questions, helperText }: MetricTableProps) => {
  const { initiativeId } = React.useContext(CustomMetricContext);

  const dispatch = useAppDispatch();
  const user = useAppSelector(getCurrentUser);

  const [errorMessage, setMessage] = React.useState();
  const [isSaving, setSaving] = React.useState(false);

  const addToGroup = async (row: UniversalTrackerPlain) => {
    if (isSaving) {
      return;
    }
    setSaving(true);
    try {
      let groupId;
      if (metricGroup?._id) {
        groupId = metricGroup._id;
      } else {
        const groupData = await createMetricGroup(initiativeId, metricGroup);
        groupId = groupData._id;
      }

      if (!groupId) {
        throw Error('No valid Metric Group');
      }
      const utrId = row._id;
      const type = row.type;
      const utrCode = row.code;
      await addMetricToGroup(initiativeId, groupId, utrId);
      getAnalytics().track(AnalyticsEvents.CustomMetricAdded, { initiativeId, groupId, utrId, type, utrCode });
      dispatch(
        addSiteAlert({
          content: (
            <>
              <strong>Success!</strong> Custom metric has been added
            </>
          ),
          color: SiteAlertColors.Primary,
          timeout: 3000,
        }),
      );
      handleReload();
    } catch (e) {
      setMessage(e.message);
      handleReload();
    }
    setSaving(false);
  };

  const removeFromGroup = async (utrId: string) => {
    if (isSaving) {
      return;
    }
    setSaving(true);
    try {
      if (!metricGroup?._id) {
        throw Error('No valid Metric Group');
      }

      await removeMetricFromGroup(initiativeId, metricGroup._id, utrId);
      handleReload();
      dispatch(
        addSiteAlert({
          content: (
            <>
              <strong>Success!</strong> Custom metric has been removed
            </>
          ),
          color: SiteAlertColors.Primary,
          timeout: 3000,
        }),
      );
    } catch (e) {
      setMessage(e.message);
    }
    setSaving(false);
  };

  const tableColumns: ColumnDef<UniversalTrackerPlain>[] = [
    {
      accessorKey: 'name',
      header: () => `${QUESTION.CAPITALIZED_SINGULAR} name`,
      meta: {
        cellProps: {
          className: 'nameCol flex-grow-1 order-1',
        },
      },
      cell: ({ row }) => (
        <NameCell
          isInGroup={isUTRInGroup({ utrId: row.original._id, metricGroup })}
          row={row}
          rowDetail={<MetricDetail utr={row.original} />}
        />
      ),
    },
    {
      accessorKey: 'type',
      header: 'Standards',
      meta: {
        cellProps: {
          className: 'typeCol row-break-1 row-lg-break-0 order-2',
        },
      },
      cell: ({ row }) => {
        const { _id: utrId, type } = row.original;

        let typeStr = type;
        if (type === 'custom_kpi') {
          typeStr = 'Custom Metric';
          const ownerId = row.original.ownerId;
          const hasPermission = user && (!ownerId || InitiativeTree.isDescendant(user, initiativeId, ownerId));
          if (!hasPermission) {
            typeStr = 'Assigned Metric';
          }
        }
        if (type === 'metric') {
          typeStr = 'Standardized Metric';
        }
        const selectedBadgeColour =
          type === 'custom_kpi' ? 'background-ThemeSuccessDark' : 'background-ThemeNeutralsExtradark';
        const unselectedBadgeColour =
          type === 'custom_kpi' ? 'background-ThemeSuccessMedium' : 'background-ThemeNeutralsDark';
        const badgeColour = isUTRInGroup({ utrId, metricGroup }) ? selectedBadgeColour : unselectedBadgeColour;

        const badges = [
          getBadge(
            standards[type]?.shortName ?? typeStr,
            standards[type]?.name ?? typeStr,
            badgeColour,
            `${utrId}-${type}`,
          ),
        ];
        if (row.original.alternatives) {
          for (const [key] of Object.entries(row.original.alternatives)) {
            const group = getGroup('standards', key);
            if (group && key !== type) {
              badges.push(getBadge(group.shortName ?? group.name, group.name, badgeColour, `${utrId}-${key}`));
            }
          }
        }
        return <>{badges.map((badge) => badge)}</>;
      },
    },
  ];

  if (questions.length) {
    tableColumns.push({
      id: '_id',
      header: '',
      meta: {
        cellProps: {
          className: 'idCol order-3',
        },
      },
      cell: ({ row }) => {
        const utrId = row.original._id;
        if (isUTRInGroup({ utrId, metricGroup })) {
          return (
            <SimpleTooltip text={`Remove Metric from group '${metricGroup?.groupName ?? 'New Group'}'`}>
              <IconButton
                disabled={isSaving}
                color='danger'
                size='xs'
                className='icon-button--size-xs'
                onClick={() => removeFromGroup(utrId)}
                icon='fal fa-times'
              />
            </SimpleTooltip>
          );
        } else {
          return (
            <SimpleTooltip text={`Add Metric to group '${metricGroup?.groupName ?? 'New Group'}'`}>
              <IconButton
                disabled={isSaving}
                size='xs'
                className='icon-button--size-xs'
                onClick={() => addToGroup(row.original)}
                icon='fal fa-plus'
              />
            </SimpleTooltip>
          );
        }
      },
    });
  }

  return (
    <>
      {errorMessage && <div className='alert alert-danger'>{errorMessage}</div>}
      <div className='metric-table-container' data-testid='table-metrics'>
        <Table
          responsive={true}
          className={isSaving ? 'isSaving' : ''}
          columns={tableColumns}
          data={questions}
          pageSize={25}
        />
        {isSaving && <Loader />}
      </div>
      {!questions.length ? <div className='metric-group-form--text-helper'>{helperText}</div> : null}
    </>
  );
};
