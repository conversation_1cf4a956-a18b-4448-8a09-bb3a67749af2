/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import React from 'react';
import { removeMetricFromGroup } from '../../actions/initiative';
import { UniversalTrackerPlain } from '../../types/universalTracker';
import { loadUniversalTrackerModal } from '../../actions/universalTrackerModal';
import { Loader } from '@g17eco/atoms/loader';
import { NameCell } from './partials/NameCell';
import './styles.scss';
import { SiteAlertColors } from '../../slice/siteAlertsSlice';
import IconButton from '../button/IconButton';
import { FormProps } from './MetricGroupForm';
import { CustomMetricContext } from './CustomMetricContainer';
import { getCurrentUser } from '../../selectors/user';
import { useAppDispatch, useAppSelector } from '../../reducers';
import { InitiativeTree } from '../../utils/initiative';
import { CurrentUserData } from '../../reducers/current-user';
import { PACK } from '@constants/terminology';
import { InputRow, TableDraggableColumn, TableDraggableRows } from '@g17eco/molecules/table-draggable-rows';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { MetricDetail } from './partials/MetricDetail';
import { MetricCell } from './partials/MetricCell';
import { isUTRInGroup } from './utils';
import { useSiteAlert } from '@hooks/useSiteAlert';

export interface DraggableMetricTableProps extends Omit<FormProps, 'customMetricsUsage' | 'handleCancel'> {
  isReadOnly: boolean;
  questions: UniversalTrackerPlain[];
  handleEditUtr: (utrId: string) => void;
  helperText?: string;
  disabledDragging?: boolean;
  handleArrange: (newColumns: string[]) => void;
}

interface MenuButtonProps {
  user?: CurrentUserData;
  initiativeId: string;
  universalTracker: UniversalTrackerPlain;
  handleEditUtr: (utrId: string) => void;
  handleRemoveUtr: (utrId: string) => void;
}

const CTAButtons = ({ universalTracker, user, handleEditUtr, initiativeId, handleRemoveUtr }: MenuButtonProps) => {
  const dispatch = useAppDispatch();

  const utrId = universalTracker._id;
  const isEditable = universalTracker.type === 'custom_kpi';
  const ownerId = universalTracker.ownerId;
  const hasPermission = user && (!ownerId || InitiativeTree.isDescendant(user, initiativeId, ownerId));

  if (!hasPermission) {
    return null;
  }

  return (
    <div className='d-flex align-items-center justify-content-center gap-2'>
      <SimpleTooltip text='Edit details'>
        <IconButton
          icon='fal fa-pen-to-square'
          color='primary'
          size='xs'
          className='icon-button--size-xs'
          outline
          onClick={() => handleEditUtr(utrId)}
          disabled={!isEditable}
          data-testid='custom-metric-edit-btn'
        />
      </SimpleTooltip>
      <SimpleTooltip text='Open data history'>
        <IconButton
          icon='fal fa-clock-rotate-left'
          color='transparent'
          size='xs'
          className='icon-button--size-xs'
          outline
          onClick={() => dispatch(loadUniversalTrackerModal({ universalTrackerId: utrId }))}
        />
      </SimpleTooltip>
      <SimpleTooltip text='Remove from group'>
        <IconButton
          icon='fal fa-times'
          color='danger'
          size='xs'
          className='icon-button--size-xs'
          onClick={() => handleRemoveUtr(utrId)}
        />
      </SimpleTooltip>
    </div>
  );
};

const customMetricsColumns: TableDraggableColumn[] = [
  {
    header: 'Code',
  },
  {
    header: 'Title',
  },
  {
    header: PACK.CAPITALIZED_PLURAL,
  },
];

const DISABLED_MESSAGE = 'Custom ordering disabled. To rearrange metrics select custom order in the top right drop-down.'

export const DraggableMetricTable = ({
  isReadOnly = false,
  metricGroup,
  handleReload,
  questions,
  handleEditUtr,
  helperText,
  handleArrange,
  disabledDragging,
}: DraggableMetricTableProps) => {
  const { initiativeId } = React.useContext(CustomMetricContext);

  const user = useAppSelector(getCurrentUser);

  const [errorMessage, setMessage] = React.useState();
  const [isSaving, setSaving] = React.useState(false);

  const { addSiteAlert } = useSiteAlert();

  const removeFromGroup = async (utrId: string) => {
    if (isSaving) {
      return;
    }
    setSaving(true);
    try {
      if (!metricGroup?._id) {
        throw Error('No valid Metric Group');
      }

      await removeMetricFromGroup(initiativeId, metricGroup._id, utrId);
      handleReload();
      addSiteAlert({
        content: (
          <>
            <strong>Success!</strong> Custom metric has been removed
          </>
        ),
        color: SiteAlertColors.Primary,
        timeout: 3000,
      });
    } catch (e) {
      setMessage(e.message);
    }
    setSaving(false);
  };

  const getColumns = () => {
    if (!questions.length) {
      return customMetricsColumns;
    }
    if (!handleEditUtr || !isReadOnly) {
      return [...customMetricsColumns, { header: '' }];
    }
    return customMetricsColumns;
  };

  const columns = getColumns();

  const rows: InputRow[] = questions.map((question) => {
    const { typeCode, _id, name } = question;
    return {
      id: _id,
      cols: [
        <div key='typeCode' className='text-truncate' style={{ maxWidth: '200px' }}>{typeCode}</div>,
        <div key='title' className='order-1' style={{ maxWidth: '350px' }}>
          <NameCell
            isInGroup={isUTRInGroup({ utrId: question._id, metricGroup })}
            row={{ original: { name } }}
            rowDetail={<MetricDetail utr={question} />}
          />
        </div>,
        <div key='type' className='row-break-1 row-lg-break-0 order-2'>
          <MetricCell
            initiativeId={initiativeId}
            isInGroup={isUTRInGroup({ utrId: question._id, metricGroup })}
            question={question}
            user={user}
          />
        </div>,
        ...(!isReadOnly
          ? [
              <div key='cta-buttons' className='buttonsCol flex-shrink-1 order-3'>
                <CTAButtons
                  user={user}
                  initiativeId={initiativeId}
                  handleEditUtr={handleEditUtr}
                  universalTracker={question}
                  handleRemoveUtr={removeFromGroup}
                />
              </div>,
            ]
          : []),
      ],
    };
  });

  return (
    <>
      {errorMessage && <div className='alert alert-danger'>{errorMessage}</div>}
      <div className='metric-table-container' data-testid='table-metrics'>
        <TableDraggableRows
          columns={columns}
          data={rows}
          handleUpdate={handleArrange}
          disabled={disabledDragging}
          disabledMessage={DISABLED_MESSAGE}
        />
        {isSaving && <Loader />}
      </div>
      {!questions.length ? <div className='metric-group-form--text-helper'>{helperText}</div> : null}
    </>
  );
};
