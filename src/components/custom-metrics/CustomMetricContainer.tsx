/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React from 'react';
import { ROUTES } from '../../constants/routes';
import { useAppSelector } from '../../reducers';
import { generateUrl } from '../../routes/util';
import { CustomMetricsUsage, MetricGroup } from '../../types/metricGroup';
import { RouteInterface } from '../../types/routes';
import { UniversalTrackerPlain } from '../../types/universalTracker';
import { LG_BREAKPOINT } from '../../utils';
import { Loader } from '@g17eco/atoms/loader';
import { ViewValues } from '../survey-overview-sidebar/viewOptions';
import { CardGridViewMode } from '../survey-scope/CardGrid';
import { ViewMode } from './constants';
import { DEFAULT_CUSTOM_METRICS_USAGE } from './utils';

interface CustomMetricsUrlParams {
  initiativeId?: string;
  isPortfolioTracker: boolean;
  surveyId?: string;
  groupId?: string;
  view?: string;
  customMetricsUsage: CustomMetricsUsage;
  buttons?: JSX.Element[];
}

interface SurveySettingsProps extends CustomMetricsUrlParams {
  children: any;
}

const isMobile = window.innerWidth <= LG_BREAKPOINT;

export interface CustomMetricContextProps extends Omit<CustomMetricsUrlParams, 'initiativeId' | 'buttons'> {
  initiativeId: string;
  buttons: JSX.Element[];
  viewLayout?: CardGridViewMode;
  setViewLayout: (layout: CardGridViewMode) => void;
  groupBy?: ViewValues;
  setGroupBy: (g: ViewValues) => void;
  metricGroups: MetricGroup[];
  blueprintQuestions: UniversalTrackerPlain[];
}

const initialState: CustomMetricContextProps = {
  initiativeId: '',
  isPortfolioTracker: false,
  viewLayout: isMobile ? CardGridViewMode.list : CardGridViewMode.gallery,
  setViewLayout: () => {},
  groupBy: ViewValues.Custom,
  setGroupBy: () => {},
  metricGroups: [],
  blueprintQuestions: [],
  customMetricsUsage: DEFAULT_CUSTOM_METRICS_USAGE,
  buttons: [],
};

export const getBaseRoute = (isPortfolioTracker: boolean): RouteInterface =>
  isPortfolioTracker ? ROUTES.PORTFOLIO_TRACKER_CUSTOM_METRICS : ROUTES.CUSTOM_METRICS;

export const getUrls = (params: Omit<CustomMetricsUrlParams, 'customMetricsUsage'>) => {
  const { isPortfolioTracker, initiativeId, groupId, surveyId } = params;
  const baseUrl = getBaseRoute(isPortfolioTracker);
  const getUrl = (view: ViewMode) => {
    const query = surveyId ? `?surveyId=${surveyId}` : '';
    return `${generateUrl(baseUrl, { initiativeId, portfolioId: initiativeId, groupId, view })}${query}`;
  };

  return {
    edit: getUrl(ViewMode.Edit),
    assignQuestions: getUrl(ViewMode.AssignQuestions),
    import: getUrl(ViewMode.Import),
    editQuestion: getUrl(ViewMode.EditQuestion),
    assignPack: getUrl(ViewMode.AssignPack),
  };
};

export const CustomMetricContext = React.createContext<CustomMetricContextProps>(initialState);

export default function CustomMetricContainer(props: SurveySettingsProps): JSX.Element | null {
  const [viewLayout, setViewLayout] = React.useState(() => initialState.viewLayout);
  const [groupBy, setGroupBy] = React.useState(() => initialState.groupBy);
  const metricGroupsState = useAppSelector((state) => state.customMetricGroups);
  const blueprintQuestionsState = useAppSelector((state) => state.initiativeBlueprintQuestions);

  if (!metricGroupsState.loaded || !blueprintQuestionsState.loaded) {
    return <Loader />;
  }

  const metricGroups = metricGroupsState.data;
  const blueprintQuestions = blueprintQuestionsState.data as UniversalTrackerPlain[];

  const contextValue: CustomMetricContextProps = {
    initiativeId: props.initiativeId ?? '',
    isPortfolioTracker: props.isPortfolioTracker,
    surveyId: props.surveyId,
    groupId: props.groupId,
    view: props.view,
    viewLayout,
    setViewLayout,
    groupBy,
    setGroupBy,
    metricGroups,
    blueprintQuestions,
    customMetricsUsage: props.customMetricsUsage,
    buttons: props.buttons ?? [],
  };

  return <CustomMetricContext.Provider value={contextValue}>{props.children}</CustomMetricContext.Provider>;
}
