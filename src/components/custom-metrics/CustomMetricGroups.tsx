/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { Button } from 'reactstrap';
import { generateUrl } from '../../routes/util';
import { AccessType, MetricGroup } from '../../types/metricGroup';
import { RouteInterface } from '../../types/routes';
import { CardGridButtonProps, CardGridItemProps } from '../survey-scope/CardGrid';
import { getCardIcon } from '../survey-scope/CustomMetricCards';
import { PACK, QUESTION, SURVEY } from '@constants/terminology';
import { InheritedIcon } from '@features/survey/scope-group/InheritedIcon';

enum CardGridButtonType {
  Duplicate = 'duplicate',
  Edit = 'edit',
  ReadOnly = 'readonly',
  Delete = 'delete',
  InheritedTooltip = 'inherited-tooltip',
}

type ClassifyCardGridButton = CardGridButtonProps & {
  type: CardGridButtonType;
};

export interface CustomMetricCardProps extends CardGridItemProps {
  accessType?: AccessType;
}

export interface MetricGroupsProps {
  initiativeId: string;
  baseRoute: RouteInterface;
  metricGroups: MetricGroup[];
  handleDeleteMetricGroup: (groupId: string) => void;
  handleCreateMetricGroup: () => void;
  handleDuplicateMetricGroup: (groupId: string) => void;
}

export const getCustomMetricGroups = ({
  initiativeId,
  baseRoute,
  metricGroups,
  handleCreateMetricGroup,
  handleDeleteMetricGroup,
  handleDuplicateMetricGroup,
}: MetricGroupsProps) => {

  const getButtons: (metricGroup: MetricGroup) => CardGridButtonProps[] = (metricGroup) => {
    const isAssigned = metricGroup.accessType === AccessType.Assigned;
    const buttons: ClassifyCardGridButton[] = [
      {
        type: CardGridButtonType.Duplicate,
        tooltip: metricGroup.groupName ? `Duplicate ${metricGroup.groupName}` : undefined,
        button: (
          <Button
            outline
            onClick={() => handleDuplicateMetricGroup(metricGroup._id)}
            className='icon-button card-grid-button'
          >
            <i className='fal fa-copy'></i>
          </Button>
        ),
      },
      {
        type: CardGridButtonType.Edit,
        icon: <i className='fal fa-cog' />,
        tooltip: `${isAssigned ? 'View' : 'Edit'} Custom Metric`,
        to: `${generateUrl(baseRoute, { initiativeId, portfolioId: initiativeId, groupId: metricGroup._id })}`,
      },
      {
        type: CardGridButtonType.ReadOnly,
        icon: <i className='fa-light fa-eye' />,
        tooltip: 'View inherited Custom Metric',
        to: `${generateUrl(baseRoute, { initiativeId, portfolioId: initiativeId, groupId: metricGroup._id })}`,
      },
      {
        type: CardGridButtonType.Delete,
        tooltip: metricGroup.groupName ? `Delete ${metricGroup.groupName} from Custom Metrics` : undefined,
        button: (
          <Button
            outline
            onClick={() => handleDeleteMetricGroup(metricGroup._id)}
            className='icon-button card-grid-button'
            color='danger'
            data-testid='delete-metric-group-btn'
          >
            <i className='fal fa-trash-can'></i>
          </Button>
        ),
      },
      {
        type: CardGridButtonType.InheritedTooltip,
        tooltip: 'This module has been inherited and cannot be deleted or edited',
        button: (
          <Button color='transparent' disabled className='px-2'>
            <i className='fa-light fa-circle-info'></i>
          </Button>
        ),
      },
    ];

    switch (metricGroup.accessType) {
      case AccessType.Custom:
        return buttons.filter((button) =>
          [CardGridButtonType.Duplicate, CardGridButtonType.Edit, CardGridButtonType.Delete].includes(button.type)
        );
      case AccessType.Inherited:
        return buttons.filter((button) =>
          [CardGridButtonType.Duplicate, CardGridButtonType.ReadOnly, CardGridButtonType.InheritedTooltip].includes(
            button.type
          )
        );
      case AccessType.Assigned:
        return buttons.filter((button) => [CardGridButtonType.Edit].includes(button.type));
      default:
        return [];
    }
  };

  // Group "Inherited" and "Custom" access type as 'custom' for filtering
  const getCardAccessType = (metricGroup: MetricGroup) => {
    if (!metricGroup.accessType || [AccessType.Inherited, AccessType.Custom].includes(metricGroup.accessType)) {
      return AccessType.Custom;
    }
    return metricGroup.accessType;
  };

  const cards: CustomMetricCardProps[] = metricGroups.map((metricGroup: MetricGroup) => {
    const isInherited = metricGroup.accessType === AccessType.Inherited;
    const isAssigned = metricGroup.accessType === AccessType.Assigned;
    const name = `${isAssigned ? `${metricGroup.initiative?.name} assigned: ` : ''}${metricGroup.groupName}`;
    const card: CustomMetricCardProps = {
      key: `scope-cardgriditem-metricgroup-${metricGroup._id}`,
      title: <span>{name}</span>,
      sortTitle: name,
      description: metricGroup.description ?? '',
      icon: getCardIcon(metricGroup),
      inheritedIcon: isInherited ? <InheritedIcon /> : null,
      unitCount: metricGroup.universalTrackers?.length ?? 0,
      unitName: QUESTION.CAPITALIZED_PLURAL,
      buttons: getButtons(metricGroup),
      scopeTag: metricGroup._id,
      inScope: false,
      isPartial: false,
      accessType: getCardAccessType(metricGroup),
    };
    return card;
  });

  // Add create new pack button to the first group
  cards.unshift({
    key: 'scope-cardgriditem-metricgroup-createnew',
    title: <span data-testid='create-metric-group-card'>Create a new {SURVEY.ADJECTIVE} {PACK.SINGULAR}</span>,
    sortTitle: `000000 Create a new ${SURVEY.ADJECTIVE} ${PACK.SINGULAR}`,
    subtitle: `Click here if you would like to create a new ${SURVEY.ADJECTIVE} ${PACK.SINGULAR} of custom metrics`,
    icon: (
      <span className='new-survey-pack-icon' onClick={handleCreateMetricGroup}>
        <i className='fa fa-plus' />
      </span>
    ),
    unitCount: undefined,
    unitName: '',
    buttons: [
      {
        icon: <span className='px-3'>Create</span>,
        tooltip: 'Create metric question groups',
        to: `${generateUrl(baseRoute, { initiativeId, portfolioId: initiativeId, groupId: 'create' })}`,
      },
    ],
    scopeTag: '',
    inScope: false,
    isPartial: false,
    accessType: AccessType.Custom,
  });

  return cards;
};
