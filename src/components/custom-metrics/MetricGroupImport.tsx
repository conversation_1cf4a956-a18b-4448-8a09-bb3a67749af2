/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import React, { useEffect, useState } from 'react';
import <PERSON> from 'papapar<PERSON>';
import G17Client from '../../services/G17Client';
import { Button, Form, FormGroup, FormText, Input, Label } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import FileZone from '../profile/FileZone';
import { MetricGroup } from '../../types/metricGroup';
import FileSaver from 'file-saver';
import { useSelector } from 'react-redux';
import { isStaff } from '../../selectors/user';
import CustomMetricContainer from './CustomMetricContainer';
import { PACK, SURVEY } from '@constants/terminology';
import { DEFAULT_CUSTOM_METRICS_USAGE } from './utils';
import { BasicAlert } from '@g17eco/molecules/alert';
import { ColumnDef, Table } from '@g17eco/molecules/table';

interface Props {
  metricGroup: MetricGroup;
  onCancel: () => void;
  onComplete: () => void;
}

interface ImportData {
  name?: string;
  QuestionCode: string
}

export const MetricGroupImport = ({ metricGroup, onCancel, onComplete }: Props) => {

  const [regenerate, setRegenerate] = useState(false);
  const [shouldReplace, setShouldReplace] = useState(false);
  const [isSaving, setSaving] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [dataArray, setData] = useState<ImportData[]>([]);
  const hasImportData = dataArray.length > 0;
  const isUserStaff = useSelector(isStaff);

  useEffect(() => {
    setData([])
  }, [metricGroup]);

  if (!isUserStaff) {
    return <BasicAlert type={'danger'}>Not permitted</BasicAlert>
  }

  const handleSetCSV = async (files: File[]) => {
    setMessage('');
    const inputFile: File | null | undefined = files[0];
    if (!inputFile) {
      setData([]);
      return;
    }
    Papa.parse<any, File>(inputFile, {
      header: true,
      skipEmptyLines: true,
      complete: (results: Papa.ParseResult<ImportData>) => {
        const data = results.data.filter(d => d.QuestionCode);
        const uniqueQuestionCodes = new Set(data.map(d => d.QuestionCode));
        if (uniqueQuestionCodes.size !== data.length) {
          const dupe = [...uniqueQuestionCodes].find(i => data.filter(d => d.QuestionCode === i).length > 1);
          return setMessage(`Duplicate row detected for QuestionCode ${dupe}. Please fix the file and upload again.`);
        }
        setData(data);
      }
    });
  }

  const handleDownloadClick = () => {
    if (!metricGroup.universalTracker || metricGroup.universalTracker.length === 0) {
      return setMessage('No metrics available to export')
    }
    const csvData = Papa.unparse({
      'fields': ['QuestionCode', 'Name'],
      'data': metricGroup.universalTracker.map(utr => ({
        QuestionCode: utr.code,
        Name: utr.name
      }))
    });
    FileSaver.saveAs(
      new Blob([csvData], { type: 'text/csv;charset=utf-8;' }),
      `${metricGroup.groupName} Export`
    );
  }

  const tableColumns: ColumnDef<ImportData>[] = [
    { accessorKey: 'QuestionCode', header: 'QuestionCode' },
    { accessorKey: 'Name', header: 'Name' },
  ];
  const canSubmit = !isSaving && dataArray.length > 0;
  const noData = dataArray.length === 0 ? 'Imported file contains no valid rows.' : '';

  const handleImport = async () => {
    setSaving(true);
    setMessage('');
    try {
      const result = await G17Client.importMetricGroup({
        groupId: metricGroup._id,
        initiativeId: metricGroup.initiativeId,
        replace: shouldReplace,
        regenerate,
        data: dataArray
      });
      setMessage(result);
      setData([]);
      onComplete();
    } catch (e) {
      setMessage('There was an error importing this data. If you think this is an error please contact our support team.');
    }
    setSaving(false);
  }

  return <CustomMetricContainer isPortfolioTracker={false} customMetricsUsage={DEFAULT_CUSTOM_METRICS_USAGE}>
    {isSaving ? <Loader /> : <></>}
    <div className='company-import-container mt-4'>
      <div className='steps'>
        <h4 className='mt-4'>Download the CSV template</h4>
        {hasImportData ?
          <div className='text-ThemeSuccessMedium'>
            <i className='fa fa-check-circle text-ThemeSuccessMedium mr-3' />Done
          </div>
          :
          <>
            <p>Download the <Button size='sm' color='link' className='p-0 pb-1' onClick={handleDownloadClick}>download CSV import file template</Button>
              <br />
              Please ensure you do not modify the '<strong>QuestionCode</strong>'.</p>
          </>
        }

        <h4 className='mt-4'>Upload the completed file</h4>
        {
          hasImportData ?
            <div className='text-ThemeSuccessMedium d-flex align-items-baseline'>
              <i className='fa fa-check-circle text-ThemeSuccessMedium mr-3' />
              Done <Button color='link' size='sm' className='ml-1 p-0 text-ThemeSuccessMedium'
                onClick={() => setData([])}> (upload again) </Button>
            </div>
            :
            <>
              <FormGroup>
                <FileZone
                  accept={{
                    'text/csv': ['.csv']
                  }}
                  disabled={isSaving}
                  handleFilesAdded={handleSetCSV}
                  dropText={() => <span>Drop a file here or click to browse for a file.</span>}
                />
              </FormGroup>
            </>
        }

        <h4 className='mt-4'>Review the data</h4>
        {hasImportData
          ?
          <>
            <p>{dataArray.length} rows found.</p>
            <Table columns={tableColumns} data={dataArray} pageSize={100} noData={noData} />
          </>
          :
          <p>Once you have uploaded your CSV, a confirmation screen will be displayed for review.</p>
        }

        <BasicAlert className={'my-2'} type='warning'>{message}</BasicAlert>
        <h4 className='mt-4'>Import the data</h4>
        <Form>
          <FormGroup check>
            <Label check>
              <Input type='checkbox' onChange={(e) => setShouldReplace(e.target.checked)} checked={shouldReplace} />
              Replace Custom Metrics in this {PACK.SINGULAR}
              <br />
              <FormText muted>This will <strong>{shouldReplace ? 'override' : 'merge'}</strong> all metrics in this {PACK.SINGULAR}.</FormText>
            </Label>
          </FormGroup>
          <FormGroup check>
            <Label check>
              <Input type='checkbox' onChange={(e) => setRegenerate(e.target.checked)} checked={regenerate} />
              Regenerate {SURVEY.PLURAL} containing this metric {PACK.SINGULAR}
            </Label>
          </FormGroup>
        </Form>
        <hr />

        <div className='mt-4 text-right'>
          <Button color='link' className='mr-3' onClick={() => onCancel()}>cancel</Button>
          <Button disabled={!canSubmit} onClick={handleImport}>Import {dataArray.length} rows</Button>
        </div>
      </div>
    </div>
  </CustomMetricContainer>

}
