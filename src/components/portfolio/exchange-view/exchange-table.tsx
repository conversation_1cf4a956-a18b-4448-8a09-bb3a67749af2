/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */
import { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import { ROUTES } from '@constants/routes';
import { generateUrl } from '@routes/util';
import { reloadPortfolioCompanies } from '@g17eco/slices/portfolioCompanySlice';
import { DataScopeAccess, DataShareScopeView, RequesterType } from '@g17eco/types/dataShare';
import { InitiativeCompany, InitiativeType } from '@g17eco/types/initiative';
import { Portfolio } from '@g17eco/types/portfolio';
import { naturalSort } from '@utils/index';
import { DataShareModal, getDefaultContent } from '../../data-share/DataShareModal';
import { MenuButton } from '../menu-button';
import AccessStatus from './partials/access-status';
import {
  ALL_METRICS_CODE,
  Column,
  ColumnType,
  GeneralColumns,
  WeightedInitiativeCompany,
  getAnsweredRatio,
  getGeneralColumnsFn,
  getLastCompletedSurvey,
} from './utils';
import { RequesterMessageModal } from '../../message-modal/RequesterMessageModal';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { isUserManagerByInitiativeId } from '../../../selectors/user';
import { getAcceptedDataShares, getViewAccess, isViewShared } from '../../../utils/dataShare';
import { updatePortfolioWeighting } from '@g17eco/slices/portfolioSlice';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { PACK, SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { ColumnDef, Table } from '@g17eco/molecules/table';
import { MAX_WEIGHT, MIN_WEIGHT, WeightColumn } from './partials/weight-column';

interface ExchangeTableProps {
  portfolio: Portfolio;
  companies: WeightedInitiativeCompany[];
  hasFilters: boolean;
  columnsSetting: Column[];
  handleReload: () => void;
}

export const ExchangeTable = ({
  portfolio,
  companies,
  hasFilters,
  columnsSetting,
  handleReload,
}: ExchangeTableProps) => {
  const dispatch = useAppDispatch();
  const isUserManager = useAppSelector((state) => isUserManagerByInitiativeId(state, portfolio._id));
  const [shareModal, setShareModal] = useState<{
    company?: Pick<InitiativeCompany, '_id' | 'name' | 'requestedDataShares'>;
  }>({});
  const [messageModal, setMessageModal] = useState<{ company?: Pick<InitiativeCompany, '_id' | 'name'> }>({});
  const toggleMessageModal = () => setMessageModal({});
  const [isSaving, setSaving] = useState(false);
  const { addSiteError } = useSiteAlert();

  const handleUpdateWeight = async (holdingId: string | undefined, originalWeight: number, weight: number) => {
    if (!holdingId || originalWeight === weight || isSaving) {
      return;
    }
    setSaving(true);
    try {
      const updatingValue = weight > MAX_WEIGHT || weight < MIN_WEIGHT ? originalWeight : weight;
      if (updatingValue !== originalWeight) {
        await dispatch(updatePortfolioWeighting({ portfolioId: portfolio._id, holdingId, weight: updatingValue }));
      }
    } catch (e) {
      addSiteError(e);
    }
    setSaving(false);
  };

  const history = useHistory();
  const handleClickCompany = (initiativeId: string) => {
    history.push(
      generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY, { portfolioId: portfolio._id, companyId: initiativeId }),
    );
  };

  const columnStyles = {
    actions: {
      className: 'text-left',
      style: {
        left: 0,
        width: 50,
        minWidth: 50,
      },
    },
    access: {
      className: 'text-center',
      style: {
        left: 50,
        width: 95,
        minWidth: 95,
      },
    },
    companyName: {
      className: 'text-left',
      style: {
        left: 145,
        width: 200,
        minWidth: 200,
      },
    },
  };

  const tableColumns: ColumnDef<InitiativeCompany>[] = [
    {
      id: 'menu-actions',
      header: () => '',
      meta: {
        headerProps: columnStyles.actions,
        cellProps: columnStyles.actions,
        sticky: 'left',
      },
      cell: ({ row }) => {
        if ('cashLine' in row.original || !isUserManager) {
          return null;
        }
        const company = row.original;
        const isPortfolio = company.type === InitiativeType.initiativeGroup;

        return (
          <MenuButton
            disabled={!isUserManager}
            onShare={(c: Pick<InitiativeCompany, '_id' | 'name' | 'requestedDataShares'>) =>
              setShareModal((s) => ({ ...s, company: c }))
            }
            isPortfolio={isPortfolio}
            portfolioId={portfolio._id}
            holdingId={company._id}
            company={company}
            setSaving={() => {}}
            handleReload={handleReload}
            dropdownProps={{ direction: 'end' }}
            handleSendMessage={(c: Pick<InitiativeCompany, '_id' | 'name'>) =>
              setMessageModal((s) => ({ ...s, company: c }))
            }
          />
        );
      },
      enableSorting: false,
    },
    {
      id: 'access',
      header: () => 'Access',
      meta: {
        headerProps: columnStyles.access,
        cellProps: columnStyles.access,
        sticky: 'left',
      },
      cell: ({ row }) => {
        return <AccessStatus company={row.original} />;
      },
      enableSorting: false,
    },
    {
      id: 'name',
      accessorKey: 'name',
      header: () => 'Company name',
      meta: {
        headerProps: columnStyles.companyName,
        cellProps: columnStyles.companyName,
        sticky: 'left',
      },
      cell: ({ row }) => {
        const company = row.original;
        if (getViewAccess(company) === DataScopeAccess.None) {
          return <span className='px-1 text-ThemeTextDark'>{company.name}</span>;
        }

        return (
          <Button color='link' className='text-left dont_translate' onClick={() => handleClickCompany(company._id)}>
            {company.name}
          </Button>
        );
      },
      sortingFn: (a, b) => naturalSort(a.original.name, b.original.name),
    },
  ];

  columnsSetting.forEach((col: Column) => {
    if (col.type === ColumnType.General) {
      if (col.code === GeneralColumns.Weight) {
        tableColumns.push({
          accessorKey: col.code,
          header: col.name,
          meta: {
            cellProps: {
              className: 'weightCol order-2 row-break-1 row-lg-break-0 ml-5 ml-lg-0 dont_translate',
            },
          },
          cell: function Cell({ row, getValue }) {
            const value = getValue() as number;
            return (
              <WeightColumn
                initiativeId={row.original._id}
                value={value}
                updateWeight={(updateValue) => handleUpdateWeight(row.original._id, value, updateValue)}
                disabled={isSaving}
              />
            );
          },
        });
        return;
      }

      tableColumns.push({
        accessorKey: col.code,
        header: col.name,
        meta: {
          cellProps: {
            className: 'text-center dont_translate',
          },
        },
        cell: ({ row }) => {
          const company = row.original;
          return isViewShared(company, DataShareScopeView.Survey) ? getGeneralColumnsFn(col.code)(company).display : '';
        },
        sortingFn: (a, b) =>
          naturalSort(getGeneralColumnsFn(col.code)(a.original).sort, getGeneralColumnsFn(col.code)(b.original).sort),
      });
    }

    if (col.type === ColumnType.SurveyPack) {
      tableColumns.push({
        accessorKey: col.code,
        header: () => {
          const hintText = (
            <span>
              Figures from <br /> latest completed <br /> {SURVEY.ADJECTIVE} {PACK.SINGULAR}
            </span>
          );
          return <SimpleTooltip text={hintText}>{col.name}</SimpleTooltip>;
        },
        meta: {
          cellProps: {
            className: 'text-center dont_translate',
          },
        },
        cell: ({ row }) => {
          const company = row.original;
          if (!isViewShared(company, DataShareScopeView.Survey)) {
            return '';
          }

          const lastCompletedSurvey = getLastCompletedSurvey(company);
          const data = lastCompletedSurvey?.[col.code];
          if (!data) {
            return '-';
          }

          const { private: privateQuestions, na, nr, answered, total } = data;
          const hintText = (
            <div className='p-2'>
              <p className='mb-1'>{privateQuestions} marked Private</p>
              <p className='mb-1'>{na} marked N/A</p>
              <p className='mb-1'>{nr} marked N/R</p>
              <p className='mb-0'>{total - answered} unanswered</p>
            </div>
          );
          return (
            <div>
              {answered}/{total}
              <SimpleTooltip text={hintText}>
                <i className='fa-light fa-circle-exclamation ml-2'></i>
              </SimpleTooltip>
            </div>
          );
        },
        sortingFn: (first, second) => {
          const firstLastCompletedSurvey = getLastCompletedSurvey(first.original);
          const secondLastCompletedSurvey = getLastCompletedSurvey(second.original);
          const fistAnswered = firstLastCompletedSurvey ? firstLastCompletedSurvey[col.code]?.answered : 0;
          const secondAnswered = secondLastCompletedSurvey ? secondLastCompletedSurvey[col.code]?.answered : 0;
          return naturalSort(fistAnswered, secondAnswered);
        },
      });
    }

    if (col.code === ALL_METRICS_CODE) {
      tableColumns.push({
        accessorKey: 'metrics',
        header: () => 'All Metrics',
        meta: {
          cellProps: {
            className: 'text-center',
          },
        },
        cell: ({ row }) => getAnsweredRatio(row.original).display,
        sortingFn: (a, b) => naturalSort(getAnsweredRatio(a.original).sort, getAnsweredRatio(b.original).sort),
      });
    }
  });

  const toggleShareModal = (update?: { reload?: boolean }) => {
    setShareModal({});
    if (update?.reload) {
      return dispatch(reloadPortfolioCompanies(portfolio._id));
    }
  };

  const acceptedDataShares = getAcceptedDataShares(shareModal?.company?.requestedDataShares);

  return (
    <>
      <div className='exchange-table-container mt-3'>
        <div className='table-wrapper mt-2'>
          <Table responsive={true} columns={tableColumns} data={companies} sticky />
          {companies?.length === 0 ? (
            <BasicAlert type='secondary' className='mt-2'>
              {hasFilters ? 'No company match your criteria' : 'You have no companies assigned to this portfolio'}
            </BasicAlert>
          ) : null}
        </div>
      </div>
      {shareModal.company && (
        <DataShareModal
          key={shareModal.company._id}
          isOpen={Boolean(shareModal.company)}
          toggle={toggleShareModal}
          requesterId={portfolio._id}
          requesterType={RequesterType.Portfolio}
          title={`${portfolio.name} is requesting ${acceptedDataShares.length ? 'additional ' : ''}access to ${
            shareModal.company.name
          }`}
          content={getDefaultContent({ requesterName: portfolio.name })}
          initiativeId={shareModal.company._id}
          initiativeName={shareModal.company.name}
          acceptedDataShares={acceptedDataShares}
        />
      )}
      {messageModal.company ? (
        <RequesterMessageModal
          title={`Message ${messageModal.company.name}`}
          requesterId={portfolio._id}
          initiativeId={messageModal.company._id}
          toggle={toggleMessageModal}
        />
      ) : null}
    </>
  );
};

