/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';
import { Button, Input, InputGroup,  InputGroupText } from 'reactstrap';
import { updateHoldingWeightFromPortfolio } from '../../actions/portfolio-tracker';
import { Avatar } from '@g17eco/atoms/avatar';
import { escapeRegexCharacters } from '../../utils/string-format';
import { ROUTES } from '../../constants/routes';
import { Portfolio, ScorecardChild } from '../../types/portfolio';
import NumberFormat from '../../utils/number-format';
import { ScorecardStatusbar } from '../scorecard-statusbar';
import { naturalSort } from '../../utils';
import { InitiativeCompany, InitiativeType } from '../../types/initiative';
import { generateUrl } from '../../routes/util';
import { DataShareMin, DataShareScopeView, DataShareStatus, DataShareWithRequester, RequesterType } from '../../types/dataShare';
import { DataShareModal, getDefaultContent } from '../data-share/DataShareModal';
import { MenuButton } from './menu-button';
import { PORTFOLIO_TRACKER_COMPANY_DOWNLOADS } from '../../apps/portfolio-tracker/routes/PortfolioTrackerCompanyRoute';
import { getCompanyStatus } from '../../utils/initiative';
import { RequesterMessageModal } from '../message-modal/RequesterMessageModal';
import { useAppSelector } from '../../reducers';
import { isUserManagerByInitiativeId } from '../../selectors/user';
import { getAcceptedDataShares, mergeScopeViews } from '../../utils/dataShare';
import { loggerMessage } from '../../logger';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { ColumnDef, Table } from '@g17eco/molecules/table';

export type Company = Pick<InitiativeCompany,
  '_id' |
  'name' |
  'userCount' |
  'lastCompletedSurveyDate' |
  'lastSurveyDate' |
  'dataShare' |
  'requestedDataShares' |
  'customer' |
  'tags'
>;

interface PortfolioHoldingsTableProps {
  companies: Company[];
  handleReload: () => void;
  companyReload: () => void;
  portfolio: Portfolio;
  searchText?: string;
  filterType?: any;
}

export const hasAllowedDataShare = (
  dataShare: DataShareWithRequester[] | DataShareMin[] | undefined,
  scopeView = [DataShareScopeView.Insights, DataShareScopeView.Survey, DataShareScopeView.Downloads]
) => {
  const allowedDataShares = dataShare?.filter((row) =>
    row.dataScope?.survey?.views?.some((view) => scopeView.includes(view) && row.status === DataShareStatus.Accepted)
  );
  return allowedDataShares && allowedDataShares.length > 0;
};

const getAllowedScopePath = (dataShare: DataShareMin[] | undefined) => {
  if (hasAllowedDataShare(dataShare, [DataShareScopeView.Insights])) {
    return ROUTES.PORTFOLIO_TRACKER_COMPANY;
  }
  if (hasAllowedDataShare(dataShare, [DataShareScopeView.Survey])) {
    return ROUTES.PORTFOLIO_TRACKER_SURVEY_OVERVIEW;
  }
  if (hasAllowedDataShare(dataShare, [DataShareScopeView.Downloads])) {
    return PORTFOLIO_TRACKER_COMPANY_DOWNLOADS;
  }
  return ROUTES.PORTFOLIO_TRACKER_COMPANY;
};

const PortfolioHoldingsTable = (props: PortfolioHoldingsTableProps) => {

  const { companies, handleReload, companyReload, portfolio, searchText, filterType } = props;

  const history = useHistory();
  const [filteredHoldings, setHoldings] = React.useState(portfolio.scorecard.children);
  const [isLoaded, setLoaded] = React.useState(false);
  const [isSaving, setSaving] = React.useState(false);
  const [shareModal, setShareModal] = useState<{ company?: Pick<InitiativeCompany, '_id' | 'name' | 'requestedDataShares'> }>({});
  const [messageModal, setMessageModal] = useState<{ company?: Pick<InitiativeCompany, '_id' | 'name'> }>({});
  const isManager = useAppSelector((state) => isUserManagerByInitiativeId(state, portfolio._id));

  const toggleShareModal = (update?: { reload?: boolean }) => {
    setShareModal({})
    if (update?.reload) {
      return companyReload()
    }
  }

  const toggleMessageModal = () => setMessageModal({});

  const totalWeight = filteredHoldings.reduce((acc, h) => acc + h.weight, 0);

  React.useEffect(() => {
    if (!portfolio.scorecard.children) {
      return;
    }
    const filter = (u: ScorecardChild) => {
      if (searchText) {
        const searchString = `${u.initiativeName}`.toLowerCase();
        const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
        const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
        return (searchString.match(searchTextRegex) || []).length > 0;
      }
      return true;
    };
    const sortedChildren = portfolio.scorecard.children
      .filter(filter)
      .sort((a, b) => naturalSort(a.initiativeName, b.initiativeName));

    setHoldings(sortedChildren);
    setLoaded(true);
  }, [portfolio, searchText, filterType, totalWeight]);

  const handleUpdateWeight = async (holdingId: string | undefined, originalWeight: number, weight: number) => {
    if (!holdingId) {
      return;
    }
    if (originalWeight === weight) {
      return;
    }
    setSaving(true);
    try {
      await updateHoldingWeightFromPortfolio({ portfolioId: props.portfolio._id, holdingId, weight });
      handleReload();
    } catch (e) {
      loggerMessage(e.message, { holdingId, portfolioId: props.portfolio._id });
    }
    setSaving(false);
  }

  const handleClick = (r: Partial<ScorecardChild>, company?: Company) => {
    if (r.initiativeType === InitiativeType.initiativeGroup) {
      history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_PORTFOLIO, { portfolioId: r.initiativeId }));
    } else {
      const views = company?.requestedDataShares;
      const path = getAllowedScopePath(views);
      history.push(generateUrl(path, { portfolioId: portfolio._id, companyId: r.initiativeId }));
    }
  }

  const tableColumns: ColumnDef<Partial<ScorecardChild> & { cashLine?: boolean }>[] = [
    {
      accessorKey: 'initiativeName',
      header: 'Holding',
      meta: {
        cellProps: {
          className: 'nameCol flex-grow-1 text-left order-1',
        },
      },
      cell: ({ row }) => {
        if ('cashLine' in row.original) {
          return (
            <div className={'d-flex align-items-center text-ThemeTextMedium'}>
              <div className='profile mr-2'>
                <i className='fa fa-money-bill-alt' />
              </div>
              <div className='flex-fill'>Cash/other</div>
            </div>
          );
        }
        const r = row.original;
        const type = r.initiativeType;
        const isPortfolio = type === InitiativeType.initiativeGroup;

        const company = companies.find((company) => company._id === r.initiativeId);
        const companyStatus = company ? getCompanyStatus(company) : null;
        const isShared = companyStatus?.data_shared ?? false;

        return (
          <div className={'d-flex align-items-center'}>
            <div className='profile mr-2'>
              {isPortfolio ? (
                <Button color='link' className='p-0' onClick={() => handleClick(r)}>
                  <i className={'fa fa-folder-open'} style={{ width: '30px' }} />
                </Button>
              ) : (
                <Avatar width='30px'>
                  {r.initiativeProfile ? <img alt='avatar' src={r.initiativeProfile} /> : <></>}
                </Avatar>
              )}
            </div>
            <div className='flex-fill'>
              {!(isShared || isPortfolio) ? (
                <span className='px-1 text-ThemeTextDark'>{r.initiativeName}</span>
              ) : (
                <Button color='link' onClick={() => handleClick(r, company)}>
                  {r.initiativeName ?? ''}
                </Button>
              )}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'actual',
      header: 'Index',
      meta: {
        cellProps: {
          className: 'actualCol text-center order-3',
        },
      },
      cell: ({ row }) => {
        if ('cashLine' in row.original) {
          return null;
        }
        return <NumberFormat value={row.original.actual} decimalPlaces={1} suffix={'%'} />;
      },
    },
    {
      id: 'goals',
      header: 'Goals',
      meta: {
        cellProps: {
          className: 'goalsCol row-break-1 row-lg-break-0 order-5 text-left',
        },
      },
      cell: ({ row }) => {
        if ('cashLine' in row.original) {
          return null;
        }
        const goals: { [key: number]: number } = {};
        row.original.goals?.forEach(({ sdgCode, actual }) => {
          goals[Number(sdgCode)] = actual || 0;
        });
        return <ScorecardStatusbar goals={goals} />;
      },
    },
    {
      accessorKey: 'weight',
      header: '% Weight',
      meta: {
        cellProps: {
          className: 'weightCol order-2 row-break-1 row-lg-break-0 ml-5 ml-lg-0',
        },
      },
      cell: ({ row }) => {
        const data = row.original;
        return (
          <InputGroup>
            <InputGroupText className='d-inline-block d-lg-none'>Weight:</InputGroupText>
            <Input
              type={'number'}
              min={0}
              className='text-center'
              disabled={isSaving || 'cashLine' in data}
              defaultValue={Math.round(data.weight || 0 * 10) / 10}
              onBlur={(e) =>
                'cashLine' in data
                  ? {}
                  : handleUpdateWeight(data.initiativeId, Number(data.weight), Number(e.target.value))
              }
            />
            <InputGroupText>%</InputGroupText>
          </InputGroup>
        );
      },
    },
    {
      id: 'menu-button',
      header: '',
      meta: {
        cellProps: {
          className: 'text-left order-5',
        },
      },
      cell: ({ row }) => {
        if ('cashLine' in row.original || !isManager) {
          return null;
        }
        const r = row.original;
        const company = companies.find((company) => company._id === r.initiativeId);
        const type = r.initiativeType;
        const isPortfolio = type === InitiativeType.initiativeGroup;

        if (!company) {
          return null;
        }

        return (
          <MenuButton
            disabled={!isManager}
            onShare={(c: Pick<InitiativeCompany, '_id' | 'name' | 'requestedDataShares'>) =>
              setShareModal((s) => ({ ...s, company: c }))
            }
            isPortfolio={isPortfolio}
            portfolioId={props.portfolio._id}
            holdingId={company._id}
            company={company}
            setSaving={setSaving}
            handleReload={handleReload}
            handleSendMessage={(c: Pick<InitiativeCompany, '_id' | 'name'>) =>
              setMessageModal((s) => ({ ...s, company: c }))
            }
          />
        );
      },
    },
  ];

  const noResults = filteredHoldings.length === 0;
  let noUsersMessage = '';
  if (noResults) {
    noUsersMessage = filterType || searchText ? 'No holdings match your criteria' : 'You have no holdings in this portfolio. Please use the form below to add companies.';
  }

  const holdings: (Partial<ScorecardChild> & { cashLine?: boolean })[] = [
    ...filteredHoldings,
    {
      cashLine: true,
      weight: totalWeight >= 100 ? 0 : 100 - totalWeight
    }
  ];

  const acceptedDataShares = getAcceptedDataShares(shareModal?.company?.requestedDataShares);
  const existedViews = acceptedDataShares.length ? mergeScopeViews(acceptedDataShares) : undefined;

  return (
    <>
      <LoadingPlaceholder isLoading={!isLoaded} height={40} />
      <LoadingPlaceholder isLoading={!isLoaded} height={40} count={5} className='mt-1 background-ThemeTextWhite'>
        {noResults ? (
          <div className='alert alert-primary'>{noUsersMessage}</div>
        ) : (
          <div className='portfolio-table-container'>
            <Table responsive={true} showRowCount={true} columns={tableColumns} data={holdings} pageSize={25} />
            <div className='text-right pt-1 text-ThemeTextMedium strong'>Total: {totalWeight}%</div>
            {totalWeight > 100 ? (
              <div className='text-right text-ThemeDangerMedium'>
                please update your holdings so they add up to 100%
              </div>
            ) : (
              <></>
            )}
          </div>
        )}
      </LoadingPlaceholder>
      {shareModal.company && (
        <DataShareModal
          key={shareModal.company._id}
          isOpen={Boolean(shareModal.company)}
          toggle={toggleShareModal}
          requesterId={portfolio._id}
          requesterType={RequesterType.Portfolio}
          title={`${portfolio.name} is requesting ${acceptedDataShares.length ? 'additional ' : ''}access to ${shareModal.company.name}`}
          content={getDefaultContent({ requesterName: portfolio.name })}
          initiativeId={shareModal.company._id}
          initiativeName={shareModal.company?.name}
          dataScope={existedViews ? { survey: { views: existedViews } } : undefined}
        />
      )}

      {messageModal.company ? (
        <RequesterMessageModal
          title={`Message ${messageModal.company.name}`}
          requesterId={portfolio._id}
          initiativeId={messageModal.company._id}
          toggle={toggleMessageModal}
        />
      ) : null}
    </>
  );
}

export default PortfolioHoldingsTable;
