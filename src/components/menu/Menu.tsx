/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import {
  Button,
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  Nav,
  NavItem,
  NavItemProps,
  UncontrolledDropdown,
} from 'reactstrap';
import './styles.scss';

interface MenuItemProps extends Pick<NavItemProps, 'onClick' | 'active' | 'label'> {
  hidden?: boolean;
  disabled?: boolean;
}

export interface MenuProps {
  grouped?: boolean;
  items: MenuItemProps[];
  color?: 'primary' | 'secondary';
  className?: string;
}

export const Menu = (props: MenuProps) => {
  const { color = 'primary', grouped, className = 'mb-2', items } = props;
  const activeItem = items.find((item) => item.active) ?? items[0];

  return (
    <>
      <Nav
        fill={!grouped}
        className={`menu-component menu-component-${grouped ? 'grouped' : 'spaced'} d-none d-md-flex ${className}`}
      >
        {items.map((item, i) => {
          return !item.hidden ? (
            <NavItem key={`${i}-${item.label}`}>
              <Button
                color={item.active ? 'primary' : color}
                className={grouped ? '' : 'w-100'}
                size={grouped ? 'sm' : 'md'}
                disabled={item.disabled}
                onClick={item.disabled || item.active ? undefined : item.onClick}
                active={item.active}
                outline={!item.active}
              >
                {item.label}
              </Button>
            </NavItem>
          ) : null;
        })}
      </Nav>

      <UncontrolledDropdown className='d-md-none'>
        <DropdownToggle
          className='d-flex align-items-center px-3 py-2 text-ThemeAccentMedium'
          color='transparent'
        >
          <i className='fas fa-bars mr-2 text-ThemeAccentMedium'></i> <span>{activeItem.label}</span>
        </DropdownToggle>
        <DropdownMenu className='border-ThemeNeutralsLight'>
          {items.map((item, i) => {
            return !item.hidden ? (
              <DropdownItem
                key={`${i}-${item.label}`}
                size={grouped ? 'sm' : 'md'}
                color={item.active ? 'primary' : color}
                active={item.active}
                disabled={item.disabled}
                onClick={item.disabled ? undefined : item.onClick}
              >
                {item.label}
              </DropdownItem>
            ) : null;
          })}
        </DropdownMenu>
      </UncontrolledDropdown>
    </>
  );
};
