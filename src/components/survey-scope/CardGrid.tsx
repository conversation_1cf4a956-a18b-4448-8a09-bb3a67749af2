/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useContext } from 'react';
import { useHistory } from 'react-router-dom';
import { Button } from 'reactstrap';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import './CardGrid.scss';

export enum CardGridViewMode { gallery, list }

export interface CardGridGroup {
  name?: string,
  cards: CardGridItemProps[],
  component?: () => JSX.Element;
}

interface CardGridProps {
  emptyMessage?: string;
  viewLayout: CardGridViewMode;
  children: JSX.Element[];
}

export interface CardGridItemProps {
  key: string;
  icon?: JSX.Element;
  title: JSX.Element | string;
  inheritedIcon?: JSX.Element | null;
  sortTitle: string;
  subtitle?: JSX.Element | string;
  description?: JSX.Element | string;
  unitName?: string;
  unitCount?: number;
  additionalInfo?: JSX.Element | string;
  buttons?: CardGridButtonProps[];
  footer?: JSX.Element | string;
  displayGridSubtitles?: boolean;
  scopeTag: string;
  className?: string;
  requiredTags?: string[];
  disabled?: boolean;
  inScope: boolean;
  isPartial: boolean;
  isMandatory?: boolean;
  version?: number;
  versionGroupCode?: string;
  initiativeId?: string;
}

type ButtonProps = {
  tooltip?: string;
  button: JSX.Element;
  icon?: never;
  onClick?: never;
  to?: string;
}

type ButtonWrapperProps = {
  tooltip?: string;
  button?: never;
  icon: JSX.Element;
  onClick?: () => void;
  to?: string;
}
export type CardGridButtonProps = ButtonProps | ButtonWrapperProps

export const CardGridContext = React.createContext({ viewLayout: CardGridViewMode.gallery });

export default function CardGrid(props: CardGridProps) {
  const { children, viewLayout, emptyMessage } = props;

  const flexModel = viewLayout === CardGridViewMode.gallery ? 'align-content-start flex-wrap' : 'flex-column';
  return (
    <div className={`card-grid d-flex ${flexModel}`}>
      <CardGridContext.Provider value={{ viewLayout }}>
        {children.length === 0 ? <CardGridErrorMessage>{emptyMessage}</CardGridErrorMessage> : children}
      </CardGridContext.Provider>
    </div>
  );
}

export function CardGridErrorMessage(props: { children: JSX.Element | string | undefined }) {
  if (!props.children) {
    return <></>;
  }
  return <div className='card-grid-error mb-3'>
    <i className='fa fa-info-circle mr-2' />{props.children}
  </div>
}

export function CardGridItem(props: CardGridItemProps) {
  const { viewLayout } = useContext(CardGridContext);

  switch (viewLayout) {
    case CardGridViewMode.list:
      return CardGridItemList(props);
    default:
      return CardGridItemGallery(props);
  }
}

export function CardGridItemList(props: CardGridItemProps) {
  const { icon, title, subtitle, description, unitCount, unitName, additionalInfo, buttons, footer, className, disabled } = props;


  return (
    <div className={`card-grid-item viewLayout-list ${className ?? ''} ${disabled ? 'disabled' : ''}`}>
      <div className='d-flex border-bottom pb-2'>
        <div className='card-icon'>{icon}</div>
        <div className='card-titles pl-2'>
          {title ? <div className='card-title strong'>{title}</div> : null}
          {subtitle ? <div className='card-subtitle strong'>{subtitle}</div> : null}
          {footer ? <div className='card-subtitle strong'>{footer}</div> : null}
        </div>
        <div className='card-unit text-right mx-2'>
          <span className='card-unit-count strong dont_translate'>{unitCount}</span>
          <span className='card-unit-name'>{unitName}</span>
          <span className='card-unit-additional-info'>{additionalInfo}</span>
        </div>
        <div className='card-buttons flex-nowrap'>
          <div className='border-start pl-1 d-flex align-items-start'>
            {buttons?.map((button, i) => <CardGridButton key={`${title}-btn-${i}`} {...button} />)}
          </div>
        </div>
      </div>
      <div className='card-description mt-2'>{description}</div>
    </div>
  );
}

export function CardGridItemGallery(props: CardGridItemProps) {
  const { icon, inheritedIcon, title, unitCount, unitName, additionalInfo, buttons, className, disabled, displayGridSubtitles, subtitle, footer } = props;

  return (
    <div
      className={`card-grid-item viewLayout-grid d-flex flex-column ${className ?? ''} ${disabled ? 'disabled' : ''}`}
    >
      <div className='flex-shrink-1 d-flex border-bottom pb-2'>
        <div className='card-icon'>{icon}</div>
        <div className='card-titles pl-2'>
          {inheritedIcon}
          {title && (
            <SimpleTooltip text={title} className='text-truncate'>
              <div className='card-title strong'>{title}</div>
            </SimpleTooltip>
          )}
          {displayGridSubtitles && (subtitle || footer) && (
            <>
              <div className='card-subtitle strong'>{subtitle}</div>
              <div className='card-subtitle strong'>{footer}</div>
            </>
          )}
        </div>
      </div>
      <div className='d-flex mt-2'>
        <div className='card-unit flex-fill d-flex flex-column'>
          <span className='card-unit-count strong dont_translate'>{unitCount}</span>
          <span className='card-unit-name'>{unitName}</span>
          <span className='card-unit-additional-info flex-fill'>{additionalInfo}</span>
        </div>
        <div className='card-buttons flex-nowrap d-flex align-items-start'>
          {buttons?.map((button, i) => (
            <CardGridButton key={`${title}-btn-${i}`} {...button} />
          ))}
        </div>
      </div>
    </div>
  );
}

export function CardGridButton(props: CardGridButtonProps) {

  const history = useHistory();
  if ('button' in props) {
    return <SimpleTooltip text={props.tooltip}>{props.button}</SimpleTooltip>
  }

  const { icon, tooltip, to, onClick } = props;
  if (!onClick && !to) {
    return (
      <SimpleTooltip text={tooltip}>
        <span className='btn btn-outline-secondary btn-icon card-grid-button' style={{ pointerEvents: 'none' }}>{icon}</span>
      </SimpleTooltip>
    );
  }

  const onClickFn = to ? () => history.push(to) : onClick;

  return (
    <SimpleTooltip text={tooltip}>
      <Button outline color={'secondary'} className='btn-icon card-grid-button' onClick={onClickFn}>{icon}</Button>
    </SimpleTooltip>
  )
}
