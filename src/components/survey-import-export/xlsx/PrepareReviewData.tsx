import { useEffect, useMemo } from 'react';
import { ReviewData } from './ReviewData';
import { useSurveyGroups } from '@utils/survey';
import { SurveyOverviewMode } from '@g17eco/slices/surveySettings';
import { convertToQuestionMapByCode } from '@selectors/survey';
import { isValidSheetData } from '@utils/file/columnMapping';
import { getSheetDataObject } from '@utils/file/XlsxFile';
import { NotApplicableTypes } from '@constants/status';
import { UniversalTrackerPlain, UtrValueType } from '@g17eco/types/universalTracker';
import { SurveyModelMinimalUtrv } from '@models/surveyData';
import { ValueListPlain } from '@g17eco/types/valueList';
import { CellValueTypes, getDataPointValue, isValidData, ReviewDataProps, ReviewValue } from './utils';

enum ReportTypeColumnOptions {
  Reporting = 'Reporting',
  NA = 'Not Applicable',
  NR = 'Not Reporting',
}

const hasValueChanged = (params: { value: ReviewValue; existingValue: ReviewValue }): boolean => {
  const { value, existingValue } = params;
  const hasNewValue = value !== undefined && value !== '';
  const hasExistingValue = existingValue !== undefined && existingValue !== '';
  return existingValue !== value && (hasExistingValue || hasNewValue);
};

const validTypes = [
  UtrValueType.Number,
  UtrValueType.Percentage,
  UtrValueType.Date,
  UtrValueType.Text,
  UtrValueType.ValueList,
  UtrValueType.Table,
  UtrValueType.NumericValueList,
  UtrValueType.TextValueList,
  UtrValueType.ValueListMulti,
];

interface ReadyToImport {
  value: CellValueTypes;
  notApplicableType: CellValueTypes;
  utr?: Pick<UniversalTrackerPlain, 'valueType' | 'valueValidation'>;
  utrv?: Pick<SurveyModelMinimalUtrv, 'value' | 'valueData'>;
  note?: string;
  existingValue: ReviewValue;
}

const isReadyToImport = ({ value, notApplicableType, utr, utrv, note, existingValue }: ReadyToImport) => {
  if (!utr || !utrv) {
    return false;
  }

  if ([ReportTypeColumnOptions.NA, ReportTypeColumnOptions.NR].includes(notApplicableType as ReportTypeColumnOptions)) {
    const hasChangedNa =
      notApplicableType === ReportTypeColumnOptions.NA && utrv.valueData?.notApplicableType !== NotApplicableTypes.na;
    const hasChangedNr =
      notApplicableType === ReportTypeColumnOptions.NR && utrv.valueData?.notApplicableType !== NotApplicableTypes.nr;
    return hasChangedNa || hasChangedNr;
  }

  if (!validTypes.includes(utr.valueType as UtrValueType)) {
    return true; // Cannot compare atm
  }

  if (note) {
    return true;
  }

  return hasValueChanged({ value, existingValue });
};

interface PrepareReviewDataProps extends ReviewDataProps {
  valueListData: ValueListPlain[] | undefined;
}

export const PrepareReviewData = (props: PrepareReviewDataProps) => {
  const { parsedData, surveyData, rows, valueListData, setData } = props;

  const surveyGroups = useSurveyGroups(SurveyOverviewMode.Universal, surveyData);
  const questionList = useMemo(() => convertToQuestionMapByCode(surveyGroups), [surveyGroups]);

  useEffect(() => {
    const worksheet = parsedData.sheet;
    const { mapping, isValid } = isValidSheetData(worksheet, parsedData.mapping);
    const results = getSheetDataObject(worksheet);

    if (!isValidData(results, isValid)) {
      return;
    }

    const data = results.filter((d) => d[mapping.utrCode] !== '');
    const utrvs = surveyData.fragmentUniversalTrackerValues ?? [];

    const valueListMap = new Map<string, ValueListPlain>(valueListData?.map((v) => [v._id, v]));

    const importRows = data.map((d) => {
      const utr = d[mapping.utrCode] ? questionList.get(String(d[mapping.utrCode])) : undefined;
      const utrv = utrvs.find((u) => u.universalTrackerId === utr?.universalTracker.getId());
      const v = d[mapping.value];
      const value = typeof v === 'string' ? v.trim() : v;
      const note = d[mapping.note] ? String(d[mapping.note]).trim() : undefined;

      const valueListCode = d[mapping.columnCode] ? String(d[mapping.columnCode]).trim() : undefined;
      const existingValue = getDataPointValue({ utr, utrv, valueListCode, valueListMap });

      return {
        ...d,
        utr: utr,
        utrv: utrv,
        originalValue: existingValue,
        [mapping.value]: value,
        skipped: !isReadyToImport({
          value,
          notApplicableType: d[mapping.notApplicableType],
          utr: utr?.universalTracker.getRaw(),
          utrv,
          note,
          existingValue,
        }),
      };
    });

    setData(importRows);
  }, [questionList, surveyData, valueListData, setData, parsedData]);

  return <ReviewData rows={rows} />;
};
