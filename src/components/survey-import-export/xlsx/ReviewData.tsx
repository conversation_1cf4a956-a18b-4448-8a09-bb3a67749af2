/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect, useMemo, useState } from 'react';
import { DataImportRow } from '../../../types/survey';
import { UtrValueTypes } from '../../../utils/universalTracker';
import {
  isValidSheetData,
  TransformMapping
} from '../../../utils/file/columnMapping';
import { getSheetDataObject } from '../../../utils/file/XlsxFile';
import { UniversalTrackerPlain } from '../../../types/universalTracker';
import { ParseData } from '../parserTypes';
import { SurveyActionData, SurveyModelMinimalUtrv } from '../../../model/surveyData';
import ButtonWithLoader from '../../button/ButtonWithLoader';
import { convertToQuestionMapByCode } from '../../../selectors/survey';
import { useSurveyGroups } from '../../../utils/survey';
import { SurveyOverviewMode } from '../../../slice/surveySettings';
import { QUESTION } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Table, ColumnDef } from '@g17eco/molecules/table';

type CellValueTypes = string | number | undefined;
type SheetData = Record<string, CellValueTypes>;
export type ExpandedData = Record<string, unknown>;


const validTypes = [
  UtrValueTypes.number,
  UtrValueTypes.percentage,
  UtrValueTypes.date,
  UtrValueTypes.text,
  UtrValueTypes.valueList,
];
const isValidData = (results: unknown[], isValid: boolean): results is SheetData[] => isValid;

const isValidValueData = (utr: Pick<UniversalTrackerPlain, 'valueType'>) => [
  UtrValueTypes.date,
  UtrValueTypes.text,
  UtrValueTypes.valueList,
].includes(utr.valueType);

interface ReadyToImport {
  value: CellValueTypes;
  optionCode: CellValueTypes;
  notApplicableType: CellValueTypes;
  utr?: Pick<UniversalTrackerPlain, 'valueType'>;
  utrv?: Pick<SurveyModelMinimalUtrv, 'value' | 'valueData'>;
}

const isReadyToImport = ({ value, notApplicableType, utr, utrv }: ReadyToImport) => {
  if (!utr || !utrv) {
    return false;
  }

  if (notApplicableType) {
    return true;
  }

  if (!validTypes.includes(utr.valueType)) {
    return true; // Cannot compare atm
  }
  const isValueDataType = isValidValueData(utr);

  const utrvValue = isValueDataType ? utrv.valueData?.data : utrv.value;
  const hasPreviousValue = utrvValue !== undefined;
  const hasNewValue = value !== undefined && value !== '';
  const valueDidNotChange = utrvValue === value || (!hasPreviousValue && !hasNewValue);

  return !valueDidNotChange;
}

interface Props {
  message: string;
  setMessage: (msg: string) => unknown;
  mappingColumns: TransformMapping;
  surveyData: SurveyActionData;
  rows: ExpandedData[];
  setData: (data: ExpandedData[]) => void;
  handleReject: (code: string) => unknown;
  parsedData: ParseData;
}

export const ReviewData = (props: Props) => {
  const {
    message,
    setMessage,
    parsedData,
    mappingColumns,
    surveyData,
    rows,
    setData,
  } = props;

  const hasImportData = rows.length > 0;
  const filteredArray = useMemo(() => rows.filter(d => d.skipped !== true), [rows]);
  const skippedCount = rows.length - filteredArray.length;

  const surveyGroups = useSurveyGroups(SurveyOverviewMode.Universal, surveyData);
  const questionList = useMemo(() => convertToQuestionMapByCode(surveyGroups), [surveyGroups]);

  useEffect(() => {
    const worksheet = parsedData.sheet;
    const { mapping, isValid, missing } = isValidSheetData(worksheet, parsedData.mapping);
    const results = getSheetDataObject(worksheet);
    if (!isValidData(results, isValid)) {
      setMessage('Missing required columns: ' + missing.flat().join(', '));
      return;
    }

    const data = results.filter(d => d[mapping.utrCode] !== '');
    const utrvs = surveyData.fragmentUniversalTrackerValues ?? [];

    const importRows = data.map((d) => {
      const utr = d[mapping.utrCode] ? questionList.get(String(d[mapping.utrCode])) : undefined;
      const utrv = utrvs.find(u => u.universalTrackerId === utr?.universalTracker.getId());
      const v = d[mapping.value];
      const value = typeof v === 'string' ? v.trim() : v;
      return {
        ...d,
        utr: utr,
        utrv: utrv,
        [mapping.value]: value,
        skipped: !isReadyToImport({
          value,
          optionCode: d[mapping.columnCode],
          notApplicableType: d[mapping.notApplicableType],
          utr: utr?.universalTracker.getRaw(),
          utrv
        })
      }
    });
    setData(importRows);

  }, [mappingColumns, setData, setMessage, surveyData, questionList, parsedData])

  const getOriginalValue = (row: Pick<DataImportRow, 'utr' | 'utrv' | 'skipped'>) => {
    if (!row.utr || !row.utrv) {
      return <span className='badge bg-warning'>{QUESTION.CAPITALIZED_SINGULAR} not in scope</span>;
    }
    if (row.skipped) {
      return <span className='badge bg-info'>{QUESTION.CAPITALIZED_SINGULAR} skipped</span>;
    }

    const isValueDataType = isValidValueData(row.utr);
    const utrvValue = isValueDataType ? row.utrv.valueData?.data : row.utrv.value;
    return <>{utrvValue}</>;
  }

  const getEllipsis = (str: unknown) => {
    if (typeof str !== 'string') {
      return '-';
    }

    const maxLength = 160
    return (str.length > maxLength ?
      <SimpleTooltip text={str}>{str.substring(0, maxLength) + '...'}</SimpleTooltip>
      : str)
  }

  const tableColumns: ColumnDef<ExpandedData>[] = [
    {
      header: QUESTION.CAPITALIZED_SINGULAR,
      meta: {
        headerProps: {
          style: {
            minWidth: 200
          }
        },
        cellProps: {
          className: 'text-wrap',
          style: {
            minWidth: 200
          }
        },
      },
      cell: ({ row }) => getEllipsis(row.original['Metric (Locked)'] ?? row.original['Question (Locked)']),
    },
    {
      accessorKey: 'Option (Locked)',
      header: 'Option',
      meta: {
        cellProps: { className: 'text-wrap' },
      },
      cell: ({ row }) => getEllipsis(row.original['Option (Locked)']),
    },
    {
      accessorKey: 'Unit',
      header: 'Unit',
      meta: {
        cellProps: {
          className: 'text-wrap',
        },
      },
    },
    {
      id: 'originalValue',
      header: 'Original Value',
      meta: {
        cellProps: {
          className: 'text-wrap',
        },
      },
      cell: ({ row }) => getOriginalValue(row.original),
    },
    {
      id: 'newValue',
      header: 'New Value',
      meta: {
        cellProps: {
          className: 'text-wrap',
        },
      },
      cell: ({ row }) => {
        if (row.original.skipped) {
          return <span className='badge bg-danger'>Rejected</span>;
        }
        return row.original['Value 1'] ?? null;
      },
    },
    {
      id: 'Further Explanation / Notes',
      header: () => <i className='far fa-comment' />,
      meta: {
        headerProps: {
          style: {
            minWidth: 1,
          },
        },
        cellProps: {
          style: {
            minWidth: 1,
          },
        },
      },
      cell: ({ row }) =>
        row.original['Further Explanation / Notes'] ? (
          <SimpleTooltip text={row.original['Further Explanation / Notes'] as string}>
            <i className='fa fa-comment text-ThemeAccentExtradark' />
          </SimpleTooltip>
        ) : null,
    },
    {
      accessorKey: 'Verified',
      header: 'Verified',
    },
    {
      accessorKey: 'External Evidence Links',
      header: 'External Evidence Links',
      cell: ({ row }) => getEllipsis(row.original['External Evidence Links']),
    },
    {
      accessorKey: 'Internal Evidence Links',
      header: 'Internal Evidence Links',
      cell: ({ row }) => getEllipsis(row.original['Internal Evidence Links']),
    },
  ];

  const noData = useMemo(() => {
    if (rows.length === 0) {
      return 'Imported file contains no valid rows.'
    }
    if (filteredArray.length === 0) {
      return 'There are no valid rows to be imported. ' +
        `This is because the ${QUESTION.SINGULAR} is not in scope, the values have not changed, ` +
        'or they have been manually skipped.'
    }
    return '';
  }, [filteredArray.length, rows.length])


  const minPageSize = 20
  const maxPageSize = filteredArray.length
  const [pageSize, setPageSize] = useState(minPageSize);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState(filteredArray);

  const toggleAllRows = () => {
    setLoading(true);
    setPageSize(pageSize === minPageSize ? maxPageSize : minPageSize);
  }

  const delayedEffect = useMemo(() => new MessageChannel(), []);
  delayedEffect.port1.onmessage = () => {
    setLoading(false);
    setTableData(filteredArray.slice(0, pageSize))
  };

  useEffect(() => {
    delayedEffect.port2.postMessage(undefined);
  }, [filteredArray, pageSize, delayedEffect]);

  return (
    <div className='reviewData'>
      {hasImportData ? (
        <>
          <p>
            {filteredArray.length} rows found.
            {skippedCount > 0
              ? ` ${skippedCount} rows were filtered either because the value has not changed, or manually skipped.`
              : ''}
          </p>
          <Table columns={tableColumns} data={tableData} pageSize={maxPageSize} noData={noData} />

          <div className='border-top pt-5 d-flex justify-content-center scope-buttons'>
            <ButtonWithLoader loading={loading} size='sm' className='ml-2' onClick={toggleAllRows}>
              <i className={`fas fa-chevron-${pageSize === minPageSize ? 'down' : 'up'} mr-2`} />
              {loading ? 'Loading...' : `${pageSize === minPageSize ? 'Full import list' : 'Collapse import list'}`}
            </ButtonWithLoader>
          </div>
        </>
      ) : (
        <>Configuration issue</>
      )}
      <BasicAlert type='warning'>{message}</BasicAlert>
    </div>
  );
}
