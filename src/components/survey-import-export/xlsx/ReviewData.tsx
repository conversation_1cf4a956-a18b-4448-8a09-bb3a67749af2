/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import React, { useEffect, useMemo, useState } from 'react';
import ButtonWithLoader from '../../button/ButtonWithLoader';
import { QUESTION } from '@constants/terminology';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Table, ColumnDef } from '@g17eco/molecules/table';
import { DataImportRow, ExpandedData, ReviewDataProps } from './utils';

const MIN_PAGE_SIZE = 20;

const getOriginalValue = (row: Pick<DataImportRow, 'utr' | 'utrv' | 'skipped' | 'originalValue'>) => {
  const { utr, utrv, skipped, originalValue } = row;
  if (!utr || !utrv) {
    return <span className='badge bg-warning'>{QUESTION.CAPITALIZED_SINGULAR} not in scope</span>;
  }
  if (skipped) {
    return <span className='badge bg-info'>{QUESTION.CAPITALIZED_SINGULAR} skipped</span>;
  }

  return originalValue;
};

const getEllipsis = (str: unknown) => {
  if (typeof str !== 'string') {
    return '-';
  }

  const maxLength = 160;
  return str.length > maxLength ? <SimpleTooltip text={str}>{str.substring(0, maxLength) + '...'}</SimpleTooltip> : str;
};

export const ReviewData = (props: Pick<ReviewDataProps, 'rows'>) => {
  const { rows } = props;

  const hasImportData = rows.length > 0;
  const filteredArray = useMemo(() => rows.filter((d) => !d.skipped), [rows]);
  const skippedCount = rows.length - filteredArray.length;

  const tableColumns: ColumnDef<ExpandedData>[] = [
    {
      header: QUESTION.CAPITALIZED_SINGULAR,
      meta: {
        headerProps: {
          style: {
            minWidth: 200,
          },
        },
        cellProps: {
          className: 'text-wrap',
          style: {
            minWidth: 200,
          },
        },
      },
      cell: ({ row }) => getEllipsis(row.original['Metric (Locked)'] ?? row.original['Question (Locked)']),
    },
    {
      accessorKey: 'Option (Locked)',
      header: 'Option',
      meta: {
        cellProps: { className: 'text-wrap' },
      },
      cell: ({ row }) => getEllipsis(row.original['Option (Locked)']),
    },
    {
      accessorKey: 'Unit',
      header: 'Unit',
      meta: {
        cellProps: {
          className: 'text-wrap',
        },
      },
    },
    {
      id: 'originalValue',
      header: 'Original Value',
      meta: {
        cellProps: {
          className: 'text-wrap',
        },
      },
      cell: ({ row }) => getOriginalValue(row.original),
    },
    {
      id: 'newValue',
      header: 'New Value',
      meta: {
        cellProps: {
          className: 'text-wrap',
        },
      },
      cell: ({ row }) => {
        if (row.original.skipped) {
          return <span className='badge bg-danger'>Rejected</span>;
        }
        return row.original['Value 1'] ?? null;
      },
    },
    {
      id: 'Further Explanation / Notes',
      header: () => <i className='far fa-comment' />,
      meta: {
        headerProps: {
          style: {
            minWidth: 1,
          },
        },
        cellProps: {
          style: {
            minWidth: 1,
          },
        },
      },
      cell: ({ row }) =>
        row.original['Further Explanation / Notes'] ? (
          <SimpleTooltip text={row.original['Further Explanation / Notes'] as string}>
            <i className='fa fa-comment text-ThemeAccentExtradark' />
          </SimpleTooltip>
        ) : null,
    },
    {
      accessorKey: 'Verified',
      header: 'Verified',
    },
    {
      accessorKey: 'External Evidence Links',
      header: 'External Evidence Links',
      cell: ({ row }) => getEllipsis(row.original['External Evidence Links']),
    },
    {
      accessorKey: 'Internal Evidence Links',
      header: 'Internal Evidence Links',
      cell: ({ row }) => getEllipsis(row.original['Internal Evidence Links']),
    },
  ];

  const noData = useMemo(() => {
    if (rows.length === 0) {
      return 'Imported file contains no valid rows.';
    }
    if (filteredArray.length === 0) {
      return (
        'There are no valid rows to be imported. ' +
        `This is because the ${QUESTION.SINGULAR} is not in scope, the values have not changed, ` +
        'or they have been manually skipped.'
      );
    }
    return '';
  }, [filteredArray.length, rows.length]);

  const dataLength = filteredArray.length;
  const [pageSize, setPageSize] = useState(MIN_PAGE_SIZE);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState(filteredArray);

  const toggleAllRows = () => {
    setLoading(true);
    setPageSize(pageSize === MIN_PAGE_SIZE ? dataLength : MIN_PAGE_SIZE);
  };

  const delayedEffect = useMemo(() => new MessageChannel(), []);
  delayedEffect.port1.onmessage = () => {
    setLoading(false);
    setTableData(filteredArray.slice(0, pageSize));
  };

  useEffect(() => {
    delayedEffect.port2.postMessage(undefined);
  }, [filteredArray, pageSize, delayedEffect]);

  return (
    <div className='reviewData'>
      {hasImportData ? (
        <>
          <p>
            {filteredArray.length} rows found.
            {skippedCount > 0
              ? ` ${skippedCount} rows were filtered either because the value has not changed, or manually skipped.`
              : ''}
          </p>
          <Table columns={tableColumns} data={tableData} pageSize={dataLength} noData={noData} />

          {dataLength < MIN_PAGE_SIZE ? null : (
            <div className='border-top pt-5 d-flex justify-content-center scope-buttons'>
              <ButtonWithLoader loading={loading} size='sm' className='ml-2' onClick={toggleAllRows}>
                <i className={`fas fa-chevron-${pageSize === MIN_PAGE_SIZE ? 'down' : 'up'} mr-2`} />
                {loading ? 'Loading...' : `${pageSize === MIN_PAGE_SIZE ? 'Full import list' : 'Collapse import list'}`}
              </ButtonWithLoader>
            </div>
          )}
        </>
      ) : (
        <>Configuration issue</>
      )}
    </div>
  );
};
