import { useEffect, useMemo, useState } from 'react';
import { useSurveyGroups } from '@utils/survey';
import { SurveyOverviewMode } from '@g17eco/slices/surveySettings';
import { convertToQuestionMapByCode } from '@selectors/survey';
import { isValidSheetData, TransformMapping } from '@utils/file/columnMapping';
import { getSheetDataObject } from '@utils/file/XlsxFile';
import { useGetValueListByIdsQuery } from '@api/value-list';
import { Loader } from '@g17eco/atoms/loader';
import { ScopeQuestion } from '@g17eco/types/surveyScope';
import { PrepareReviewData } from './PrepareReviewData';
import { isValidData, ReviewDataProps, SheetData } from './utils';

interface CollectValueListIdsParams {
  data: SheetData[];
  questionList: Map<string, ScopeQuestion>;
  mapping: TransformMapping<string>;
}

const collectValueListIds = (params: CollectValueListIdsParams): string[] => {
  const { data, questionList, mapping } = params;
  const valueListIdsSet = new Set<string>();

  data.forEach((d) => {
    const utr = d[mapping.utrCode] ? questionList.get(String(d[mapping.utrCode])) : undefined;

    const valueListId = utr?.valueValidation?.valueList?.listId;
    if (valueListId) {
      valueListIdsSet.add(valueListId);
    }
    utr?.valueValidation?.table?.columns?.forEach((c: { listId?: string }) => {
      if (c.listId) {
        valueListIdsSet.add(c.listId);
      }
    });
  });

  return Array.from(valueListIdsSet);
};

export const ReviewDataContainer = (props: ReviewDataProps) => {
  const { setMessage, parsedData, surveyData } = props;

  const [valueListIds, setValueListIds] = useState<string[]>([]);
  const { data: valueListData, isFetching: isFetchingValueList } = useGetValueListByIdsQuery(valueListIds, {
    skip: valueListIds.length === 0,
  });

  const surveyGroups = useSurveyGroups(SurveyOverviewMode.Universal, surveyData);
  const questionList = useMemo(() => convertToQuestionMapByCode(surveyGroups), [surveyGroups]);

  useEffect(() => {
    const worksheet = parsedData.sheet;
    const { mapping, isValid, missing } = isValidSheetData(worksheet, parsedData.mapping);
    const results = getSheetDataObject(worksheet);

    if (!isValidData(results, isValid)) {
      setMessage('Missing required columns: ' + missing.flat().join(', '));
      return;
    }

    const data = results.filter((d) => d[mapping.utrCode] !== '');
    const dataValueListIds = collectValueListIds({ data, questionList, mapping });

    if (dataValueListIds.length > 0) {
      setValueListIds(dataValueListIds);
    }
  }, [questionList, parsedData, setMessage]);

  if (isFetchingValueList) {
    return <Loader relative />;
  }

  return <PrepareReviewData {...props} valueListData={valueListData} />;
};
