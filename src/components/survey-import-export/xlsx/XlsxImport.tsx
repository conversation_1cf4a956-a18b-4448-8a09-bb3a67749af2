/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */


import React, { useCallback, useContext, useMemo, useState } from 'react';
import { Button, FormGroup } from 'reactstrap';
import FileZone from '../../profile/FileZone';
import G17Client, { formDataConfig } from '../../../services/G17Client';
import { Loader } from '@g17eco/atoms/loader';
import { SurveyContext } from '../../survey-container/SurveyContainer';
import { getSheetName, XlsxFile } from '../../../utils/file/XlsxFile';
import { SurveyActionData } from '../../../model/surveyData';
import { ParseData } from '../parserTypes';
import { ReviewData, ExpandedData } from './ReviewData';
import { FileMappingData, getTransformMapping, resolveMapping } from '../../../utils/file/columnMapping';
import { SurveyImport } from '../../../types/survey';
import { QUESTION, SURVEY } from '@constants/terminology';
import { BasicAlert } from '@g17eco/molecules/alert';

const Step = ({ step }: { step: number }) => <div className='step'>{step}</div>;
const isSurveyLoaded = (surveyData: Partial<SurveyActionData>): surveyData is SurveyActionData => {
  return typeof surveyData._id === 'string' && surveyData._id !== '';
};

const XlsxImport = ({ mappingData }: { mappingData?: FileMappingData }) => {

  const { surveyData, isUserManager, reloadSurvey } = useContext(SurveyContext);

  const [isSaving, setSaving] = useState(false);
  const [message, setMessage] = useState<string>('');
  const [dataArray, setData] = useState<ExpandedData[]>([]);
  const [parseData, setParseData] = useState<ParseData | undefined>();
  const hasImportData = dataArray.length > 0;
  const filteredArray = useMemo(() => dataArray.filter(d => d.skipped !== true), [dataArray]);

  const tabName = mappingData?.tabName;
  const tabNameOptions = useMemo(() => {
    if (!tabName) {
      return [QUESTION.PLURAL]
    }
    return Array.isArray(tabName) ? tabName : [tabName]
  }, [tabName])

  const optionMapping = mappingData?.mapping;
  const mappingColumns = useMemo(() => getTransformMapping(optionMapping) ,[optionMapping]);

  const handleReject = useCallback((code: string) => {
    if (parseData) {
      setData((data) => data.map(d => {
        if (d[parseData.mapping.utrCode] === code) {
          d.skipped = true;
        }
        return d;
      }));
    }
  }, [parseData]);

  if (!isSurveyLoaded(surveyData)) {
    return <Loader />
  }

  const surveyId = surveyData._id;

  const onFilesAdded = async (files: File[]) => {
    setMessage('');
    const inputFile = files[0];
    if (!inputFile) {
      setData([]);
      return;
    }

    XlsxFile(inputFile, (wb) => {
      // Get worksheet
      const sheetName = getSheetName(wb.SheetNames, tabNameOptions);

      /* Get worksheet */
      const worksheet = wb.Sheets[sheetName || wb.SheetNames[0]];
      // Save what we have, allow fixing column issues later
      setParseData({
        mapping: resolveMapping(worksheet,mappingColumns),
        tabName: sheetName,
        file: inputFile,
        workbook: wb,
        sheet: worksheet,
      });
    });
  }

  const handleDownloadClick = () => window.open(G17Client.getSurveyXlsxImportTemplate(surveyId))
  const canSubmit = !isSaving && isUserManager && filteredArray.length > 0;

  const handleImport = async () => {
    setSaving(true);
    setMessage('');
    if (!parseData?.file) {
      return setMessage('Missing import file');
    }

    try {
      const formData: SurveyImport = {
        file: parseData.file,
        mapping: parseData.mapping,
        tabName: parseData.tabName,
      };
      const result = await G17Client.importSurveyFile(surveyId, formData, formDataConfig);
      reloadSurvey();
      setMessage(result);
      setData([]);
    } catch (e) {
      setMessage('There was an error importing this data. Please contact our support team.');
    }
    setSaving(false);
  }

  return <div>
    {isSaving ? <Loader /> : <></>}
    <div className='mt-4'>
      <div className='steps text-ThemeTextMedium'>
        <h5><Step step={1} />Download Excel template</h5>
        {hasImportData ? (
          <div className='text-ThemeSuccessMedium'>
            <i className='fa fa-check-circle text-ThemeSuccessMedium mr-3' />
            Done
          </div>
        ) : (
          <>
            <p>
              <div className='d-flex'>
                <span>Download the </span>
                <Button size='sm' color='link' className='p-0 mx-1 border-0' onClick={handleDownloadClick}>
                  Excel import file template
                </Button>
                <span> and fill in with your data.</span>
              </div>
              Do not modify columns A-E as this may result in issues with your import.
            </p>
          </>
        )}
        <h5 className='mt-4'>
          <Step step={2} />Upload completed Excel template</h5>
        {
          !isUserManager ? <BasicAlert type='warning'>Only Admins can import files</BasicAlert> :
            hasImportData ?
              <div className='text-ThemeSuccessMedium d-flex align-items-center'>
                <i className='fa fa-check-circle text-ThemeSuccessMedium mr-3' />Done <Button color='link' size='sm' className='ml-1 p-0 text-ThemeSuccessMedium' onClick={() => setData([])}>(upload again)</Button>
              </div>
              :
              <>
                <FormGroup>
                  <FileZone
                    accept={{
                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
                      'application/vnd.ms-excel': ['.xls']
                    }}
                    disabled={isSaving || !surveyId}
                    handleFilesAdded={onFilesAdded}
                    dropText={() => <div className='d-flex'>Attach template by dragging and dropping, or <Button size='sm' color='link' className='p-0 mx-1 border-0' onClick={() => {}}>
                    upload your template here
                  </Button></div>}
                    renderFileList={() => <></>}
                  />
                </FormGroup>
              </>
        }
        <h5 className='mt-4'><Step step={3} />Review the data</h5>
        {parseData ? <ReviewData
            rows={dataArray}
            setData={setData}
            setMessage={setMessage}
            parsedData={parseData}
            handleReject={handleReject}
            mappingColumns={mappingColumns}
            message={message}
            surveyData={surveyData} />
          : <p>Once you have uploaded your file, a confirmation screen will be displayed for review.</p>
        }


        <h5 className='mt-4'><Step step={4} />Import the data</h5>
        <p><b>Warning:</b> Importing this data will override your existing {SURVEY.SINGULAR} answers.</p>
        <div className='mt-4 text-right'>
          <Button disabled={!canSubmit} onClick={handleImport}>Import {filteredArray.length} rows</Button>
        </div>
      </div>
    </div>
  </div>
}

export default XlsxImport;
