/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */


import { MetricGroup } from '@g17eco/types/metricGroup';
import { faker } from '@faker-js/faker';
import { initiativeOne } from '@fixtures/initiative-factory';

export const createMetricGroup = (g : Partial<MetricGroup> = {}) => {
  return {
    _id: faker.database.mongodbObjectId(),
    groupName: faker.company.name(),
    initiativeId: initiativeOne._id,
    universalTrackers: [],
    updated: new Date().toISOString(),
    ...g,
  } satisfies MetricGroup;
}


export const metricGroupOne = createMetricGroup({
  initiativeId: initiativeOne._id,
  groupName: 'Metric Group One',
})
