/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */


import { OktaStorage } from './OktaStorage';
import { BaseApi } from './g17eco/BaseApi';
import {
  DisplaySettings,
  InitiativePlain,
  InitiativeRating,
  InitiativeUTRLookup,
  RatingAgency,
  RatingAgencyRating,
  RootInitiativeData
} from '../types/initiative';
import {
  DataPeriods,
  UniversalTrackerBlueprintMin,
  UniversalTrackerPlain
} from '../types/universalTracker';
import {
  BlueprintContributions,
  BulkSurveyImport,
  CreateCombineSurvey,
  CSVFormat,
  MassDelegation,
  SurveyImport,
  SurveyInitiative,
  SurveyListItem,
  SurveySummary,
} from '../types/survey';
import { StandardsRating } from '../types/standard';
import { UniversalTrackerValuePlain } from '../types/surveyScope';
import { FileMappingData } from '../utils/file/columnMapping';
import { RegisterInterestData } from '../types/open';
import { Action } from '../constants/action';
import type { IdentityProvider } from '../types/sso';
import { ReportTypes } from '../types/statsTypes';
import {
  AssurancePermissionsData,
  AssurancePortfolioComplete,
  AssurancePortfolioCreate,
  AssurancePortfolioUpdate,
  AssuranceStakeholdersData,
  UpdateStakeholderData,
} from '../types/assurance';
import { CustomReportsValue, MetricSubmitData } from '../types/custom-report';
import { ImportMetricGroupParams } from '../types/metricGroup';
import type { ConfigFormData, IssuerListItem } from '../components/onboarding-esgenome/types';
import { CheckoutSession, CtlOnboardingResponse, CustomerPortalSession } from './g17eco/types';
import {
  DataShareLookup,
  RequesterLookup
} from '../types/dataShare';
import { UserAgreement } from '../reducers/current-user';
import config from '../config';
import { MessagePayload } from '../components/message-modal/types';
import { StatusStats } from '../api/initiative-stats';
import { RequireAtLeastOne } from '../types/common';
import { DownloadMultiScope, HandleDownloadConfig } from '../types/download';
import { UserRoles } from '../constants/user';
import { AxiosRequestConfig } from 'axios';
import { flattenFiles } from '../utils/files';
import { AuthenticatedRequestToJoinData, OnboardingMatches, RequestToJoinData } from '../components/onboarding/types';
import { ActivationToken } from '../types/onboarding';
import { ScopeWheelPreferences, SurveyModelMinimalUtrv } from '../model/surveyData';
import { HistoricalReportData, ReportData } from '../types/reportData';
import { SDGReport } from '../components/report-output/reportApi';
import { AIResponse } from '../types/ai';
import fileDownload from 'js-file-download';

interface UniversalTrackerToUsage {
  initiativeId: string;
  utrIds: string[];
  usage: string;
}

export interface Flags {
  evidenceRequired: boolean;
  verificationRequired: boolean;
  noteRequired: boolean;
  isPrivate: boolean;
}

export type FlagProperties = RequireAtLeastOne<Flags>;

export interface SurveyQuestionFlags {
  surveyId: string,
  properties: FlagProperties,
  utrvIds: string[]
}

interface StakeholderSurveyStats {
  _id: string;
  status: StatusStats;
  isContributor: boolean;
  isVerifier: boolean;
  firstName: string;
  surname: string;
}

interface StakeholderQueryStats {
  initiativeId: string;
  surveyIds: string[];
}
interface DownloadMultiParams {
  type: 'csv' | 'xlsx',
  surveyId: string,
  downloadScope: DownloadMultiScope
}

interface DateParams {
  endDate?: Date | undefined;
  startDate?: Date;
}

type SubsriptionSession = { url: string };

export const formDataHeaders = { 'Content-Type': 'multipart/form-data' };
export const formDataConfig = { headers: formDataHeaders };

type DownloadDataShareSurveySimpleParams = {
  requesterType: string,
  requesterId: string,
  initiativeId: string,
  surveyId: string,
  type: string,
  downloadScope?: DownloadMultiScope
};

export interface RequesterDownloadData {
  targets: SurveyModelMinimalUtrv[];
  historical: HistoricalReportData[];
  survey: SurveyInitiative;
  blueprintContributions: BlueprintContributions;
}

export interface RequesterSurveyReportData {
  survey: SurveyInitiative;
  reportData: ReportData[];
}

export type RequesterReportDataParams = Omit<DownloadDataShareSurveySimpleParams, 'type'>;

interface AcceptCustomAgreementParams {
  initiativeId: string;
  agreementCode: string;
  type: string;
}

export class G17Api extends BaseApi {
  /**
   * Check if activation token is valid
   */
  public async checkActivationToken(token: string): Promise<ActivationToken> {
    return this.get(`auth/activation-token/${token || ''}`).then(r => r.data.data);
  }

  public getDownloadUrl(path: string) {
    return `${this.config.baseURL}${path}?token=${this.tokenStorage.getAccessToken()}`;
  }

  /**
   * Fetch all available rating agencies.
   */
  public async getRatingAgencies(): Promise<RatingAgency[]> {
    return this.get('/initiatives/ratings/agencies').then(r => r.data.data);
  }

  /**
   * Load initiative RatingAgencyRatings
   */
  public async getRatings(initiativeId: string): Promise<RatingAgencyRating[]> {
    return this.get(`/initiatives/${initiativeId}/ratings`)
      .then(r => r.data.data);
  }

  /**
   * Update rating information
   * It replaces existing ones with provided ratings.
   */
  public async updateRatings(initiativeId: string, ratings: InitiativeRating[]) {
    return this.patch(`/initiatives/${initiativeId}/ratings`, { ratings });
  }

  public async createCombinedReport(initiativeId: string, dataMap: CreateCombineSurvey): Promise<SurveyInitiative> {
    return this.post(`initiatives/${initiativeId}/aggregated-survey/aggregate/`, dataMap)
      .then((response) => response.data.data)
  }

  public async getSurveyListForChildren(initiativeId: string): Promise<SurveySummary[]> {
    return this.get(`/initiatives/${initiativeId}/surveys/children`)
      .then((response) => response.data.data)
  }

  /**
   * Update specific rating provider information
   * It replace existing ones with provided ratings.
   */
  public async editRating(initiativeId: string, rating: InitiativeRating) {
    return this.patch(`/initiatives/${initiativeId}/ratings/${rating.code}/${rating._id || ''}`, { rating });
  }

  /**
   * Delete specific rating provider information
   */
  public async deleteRating(initiativeId: string, ratingCode: string, itemId?: string) {
    return this.delete(`/initiatives/${initiativeId}/ratings/${ratingCode}/${itemId || ''}`);
  }

  public async getBlueprintQuestionsWithCustomMetrics(surveyId: string): Promise<UniversalTrackerBlueprintMin[]> {
    return this.get(`/surveys/${surveyId}/questions`)
      .then(r => r.data.data);
  }

  public async updateSurveyScopeWheels(surveyId: string, data: ScopeWheelPreferences[]): Promise<boolean> {
    return this.post(`surveys/${surveyId}/scope-wheels`, data)
      .then((response) => response.data.data)
  }

  public async getCustomMetricSurveyGroups(surveyId: string): Promise<any[]> {
    return this.get(`surveys/${surveyId}/metric-groups`)
      .then(r => r.data.data)
  }

  public async getSurveyListItem(surveyId: string): Promise<SurveyInitiative> {
    return this.get(`/surveys/${surveyId}/initiative`)
      .then((response) => response.data.data)
  }

  public async addUniversalTrackerToUsage({ initiativeId, utrIds, usage }: UniversalTrackerToUsage) {
    const body = {
      utrIds: utrIds,
      usage: usage
    }

    return this.patch(`initiatives/${initiativeId}/universal-trackers/usage/`, body)
      .then(r => r.data.data)
  }

  public async removeUniversalTrackerToUsage({ initiativeId, utrIds, usage }: UniversalTrackerToUsage) {
    const body = {
      utrIds: utrIds,
      usage: usage
    }
    return this.delete(`initiatives/${initiativeId}/universal-trackers/usage/?confirm=true`, { data: body })
      .then(r => r.data.data)
  }

  public async getInitiativeUtrHistory(initiativeId: string, utrId: string, options?: AxiosRequestConfig): Promise<any[]> {
    return this.get(`universal-trackers/${utrId}/history/initiativeId/${initiativeId}`, { ...options }).then(
      (r) => r.data.data
    );
  }

  public async downloadInitiativeSubsidiaries(initiativeId: string) {
    return this.get(`/initiatives/${initiativeId}/download`, { responseType: 'blob' }).then(this.downloadBlob);
  }

  public async downloadUtrvHistory(initiativeId: string, utrId: string) {
    return this.get(`universal-trackers/${utrId}/history/initiativeId/${initiativeId}/download`, { responseType: 'blob' })
      .then(this.downloadBlob)
  }

  public async getSurveyListWithStandards(initiativeId: string): Promise<{ list: SurveyListItem[], standards: StandardsRating[] }> {
    return this.get(`/initiatives/${initiativeId}/standards`)
      .then((response) => response.data.data)
  }

  public async getSurveyRatings({ initiativeId, surveyId }: { initiativeId: string, surveyId: string }) {
    return this.get(`/initiatives/${initiativeId}/standards/${surveyId}`)
      .then((response) => response.data.data)
  }

  public async getLatestTargetValue(
    { initiativeId, utrId, type }: { initiativeId: string, utrId: string, type: string }
  ): Promise<UniversalTrackerValuePlain> {
    return this
      .get(`/universal-tracker-values/universal-tracker/${utrId}/initiativeId/${initiativeId}/${type}/latest`)
      .then((result) => result.data.data)
  }

  public getSurveyXlsxImportTemplate(surveyId: string) {
    return this.getDownloadUrl(`surveys/${surveyId}/export/xlsx`);
  }

  public getBulkOnboardingImportTemplate(initiativeId: string) {
    return this.getDownloadUrl(`initiatives/${initiativeId}/users/export/xlsx`);
  }

  public async getSurveyFileMapping() {
    return this.get<{ data: FileMappingData }>('/surveys/import/file/mapping')
      .then((result) => result.data.data);
  }

  public async importSurveyData(surveyId: string, data: { data: CSVFormat[], mapping?: Record<string, string> }) {
    return this.patch(`/surveys/${surveyId}/export/import-csv`, data)
      .then((result) => result.data.message);
  }

  public async exportCustomMetrics(initiativeId: string) {
    return this.get<string>(`/initiatives/${initiativeId}/custom-metrics/export`, { responseType: 'blob' })
      .then((result) => fileDownload(result.data, 'custom-metrics.g17'))
  }

  public async importCustomMetrics(initiativeId: string, file: File, options: AxiosRequestConfig = formDataConfig) {
    return this.patch(`/initiatives/${initiativeId}/custom-metrics/import`, { file }, options)
      .then((result) => result.data);
  }

  public async importMetricGroup(data: ImportMetricGroupParams) {
    const { initiativeId, groupId, ...updateData } = data;
    return this.patch(`/initiatives/${initiativeId}/metric-groups/${groupId}/import`, updateData)
      .then((result) => result.data.message);
  }

  public async importSurveyFile(surveyId: string, formData: SurveyImport, options: AxiosRequestConfig = formDataConfig) {
    return this.post(`/surveys/${surveyId}/import/file`, formData, options)
      .then((result) => result.data.message);
  }

  public async importBulkValidate(formData: BulkSurveyImport, options: AxiosRequestConfig = formDataConfig) {
    return this.post(`/initiatives/${formData.initiativeId}/bulk-import/validate`, formData, options)
      .then((result) => result.data.data);
  }

  public async importBulk(formData: BulkSurveyImport, options: AxiosRequestConfig = formDataConfig) {
    return this.post(`/initiatives/${formData.initiativeId}/bulk-import/file`, formData, options)
      .then((result) => result.data.data);
  }

  public getBulkImportTemplate(initiativeId: string) {
    return this.getDownloadUrl(`initiatives/${initiativeId}/bulk-import/export/xlsx`);
  }

  public async getPortfolioTree(): Promise<InitiativePlain[]> {
    return this.get('initiative-tree/portfolios').then((result) => result.data.data);
  }

  public async downloadPortfolioContributions(holdingId: string) {
    return this.get(`reports/initiative/${holdingId}/scorecard`, { responseType: 'blob' }).then(this.downloadBlob);
  }

  public async getAdminStats(reportType: ReportTypes, params: object = {}) {
    return this.post(`/admin/stats/${reportType}`, params)
      .then((response) => response.data.data)
  }

  public async downloadManagerInitiativeStats(initiativeId: string, params: DateParams = {}) {
    return this.get(`/initiatives/${initiativeId}/stats/download`, { params, responseType: 'blob' })
      .then(this.downloadBlob)
  }

  public async downloadManagerSurveyStats(initiativeId: string, userId: string, surveyId: string) {
    return this.get(`/initiatives/${initiativeId}/stats/users/${userId}/survey/${surveyId}/download`, { responseType: 'blob' })
      .then(this.downloadBlob)
  }

  public async getStakeholdersStats(data: StakeholderQueryStats): Promise<StakeholderSurveyStats[]> {
    const { initiativeId, surveyIds } = data;
    return this.post(`/initiatives/${initiativeId}/stats/stakeholders`, { surveyIds })
      .then((result) => result.data.data);
  }

  public async downloadSurveyQuestions(surveyId: string, ids: string[]) {
    return this.post(`/surveys/${surveyId}/download`, { ids }, { responseType: 'blob' })
      .then(this.downloadBlob)
  }

  public async getProofByUtrvId(utrvId: string) {
    return this.get(`/universal-tracker-values/${utrvId}/proof`)
      .then((result) => result.data.data);
  }

  public async utrvAssuranceAction({ utrvId, action }: { utrvId: string, action: 'open' | 'close' }) {
    return this.patch(`/universal-tracker-values/${utrvId}/assurance`, { action })
      .then(response => response.data.data);
  }

  // @deprecated ESGenome only
  public async createSGXESGenomeSurvey(formData: ConfigFormData): Promise<CtlOnboardingResponse> {
    return this.post('/direct/onboarding/user/sgx-esgenome/register', formData)
      .then(response => response.data.data)
  }

  public async createUser(formData: ConfigFormData, appOnboardingPath: string): Promise<CtlOnboardingResponse> {
    return this.post(`/direct/onboarding/${appOnboardingPath}/register-user`, formData)
      .then(response => response.data.data)
  }

  public async createCompanyWithSurvey(formData: ConfigFormData, appOnboardingPath: string): Promise<CtlOnboardingResponse> {
    return this.post(`/direct/onboarding/${appOnboardingPath}/register-company`, formData)
      .then(response => response.data.data)
  }

  public async createUserAndCompanyWithSurvey(formData: ConfigFormData, appOnboardingPath: string): Promise<CtlOnboardingResponse> {
    return this.post(`/direct/onboarding/${appOnboardingPath}/register-user-and-company`, formData)
      .then(response => response.data.data)
  }

  public async registerInterest(formData: RegisterInterestData) {
    return this.post('/o/register/interest', formData)
      .then(response => response.data.data)
  }

  public async userInterest(formData: { title: string, message: string, company?: string }) {
    return this.post('/users/current/interest', formData).then(response => response.data.data);
  }

  public async getCompaniesByUserEmail(appOnboardingPath: string): Promise<OnboardingMatches[]> {
    return this.get(`/direct/onboarding/${appOnboardingPath}/find-existing`)
      .then(response => response.data.data)
  }

  public async getInvitations(appOnboardingPath: string): Promise<OnboardingMatches[]> {
    return this.get(`/direct/onboarding/${appOnboardingPath}/invitations`)
      .then(response => response.data.data)
  }

  public async massDelegation(surveyId: string, action: Action, data: MassDelegation) {
    return this.request({
      url: `surveys/${surveyId}/universal-tracker-values/delegation`,
      data,
      method: action === Action.Remove ? 'delete' : 'post',
    }).then(r => r.data.data)
  }

  public async updateSurveyQuestionFlags(data: SurveyQuestionFlags) {
    const { surveyId, ...rest } = data;
    return this.post(`/surveys/${surveyId}/universal-tracker-values/flags/`, rest)
      .then((result) => result.data.data);
  }

  public async downloadSurveySimple({ surveyId, type, downloadScope }: DownloadMultiParams) {
    return this.post(
      `/surveys/${surveyId}/download/${type}/simple`,
      downloadScope,
      { responseType: 'blob' }
    ).then(this.downloadBlob)
  }

  public generateXhtmlReport(params: Omit<DownloadMultiParams, 'type'> & HandleDownloadConfig) {
    const { surveyId, debug, downloadScope } = params;
    return this.post(`/surveys/${surveyId}/report/xhtml`, downloadScope, {
      responseType: 'blob',
      params: {
        debug: debug ? 'true' : undefined,
      }
    }).then(this.downloadBlob);
  }

  public async downloadLatestAssessmentAnswers(params: { surveyId: string, initiativeId: string, type: 'xlsx' | 'csv' }) {
    const { surveyId, initiativeId, type } = params;
    return this.get(`/materiality-assessment/initiatives/${initiativeId}/surveys/${surveyId}/metric-answers-report/${type}`, {
      responseType: 'blob',
    }).then(this.downloadBlob);
  }

  public async downloadDataShareSurveySimple(request: DownloadDataShareSurveySimpleParams) {
    const { downloadScope, requesterType, requesterId, initiativeId, type, surveyId } = request;
    return this.post(
      `/data-share/requester/${requesterType}/${requesterId}/initiative/${initiativeId}/survey/${surveyId}/download/${type}`,
      { downloadScope },
      { responseType: 'blob' }
    ).then(this.downloadBlob)
  }

  public async reportDataWithHistoryScope(request: RequesterReportDataParams): Promise<RequesterDownloadData> {
    const { downloadScope, requesterType, requesterId, initiativeId, surveyId } = request;
    return this.post(
      `/data-share/requester/${requesterType}/${requesterId}/initiative/${initiativeId}/survey/${surveyId}/report/historical`,
      { downloadScope },
    ).then((response) => response.data.data);
  }

  public async requesterReportData(request: RequesterReportDataParams): Promise<RequesterSurveyReportData> {
    const { downloadScope, requesterType, requesterId, initiativeId, surveyId } = request;
    return this.post(
      `/data-share/requester/${requesterType}/${requesterId}/initiative/${initiativeId}/survey/${surveyId}/report`,
      { downloadScope },
    ).then((response) => response.data.data);
  }

  public async requesterReportDataSDG(
    { requesterType, requesterId, initiativeId, surveyId }: Omit<RequesterReportDataParams, 'downloadScope'>
  ): Promise<{ sdgReport: SDGReport, survey: SurveyInitiative }> {
    return this.post(
      `/data-share/requester/${requesterType}/${requesterId}/initiative/${initiativeId}/survey/${surveyId}/sdg`,
    ).then((response) => response.data.data);
  }

  public async requesterDownloadSDG(
    { requesterType, requesterId, initiativeId, surveyId }: Omit<RequesterReportDataParams, 'downloadScope'>
  ) {
    return this.get(
      `/data-share/requester/${requesterType}/${requesterId}/initiative/${initiativeId}/survey/${surveyId}/sdg/csv`,
      { responseType: 'blob' }
    ).then(this.downloadBlob);
  }

  public async downloadFile(url: string) {
    return this.get(url, { responseType: 'blob' }).then(this.downloadBlob);
  }

  public async updateDisplaySettings(initiativeId: string, data: Partial<DisplaySettings>) {
    return this.patch(`/initiatives/${initiativeId}/display`, data)
      .then((result) => result.data.data);
  }

  public async getPublicScorecard(initiativeId: string, token: string) {
    return this.get(`/o/scorecard/${initiativeId}/${token}`)
      .then((result) => result.data.data);
  }

  public async createCalculationUtr(initiativeId: string, data: MetricSubmitData) {
    return this.post(`/initiatives/${initiativeId}/calculated-metric`, data)
      .then(response => response.data.data);
  }

  public async createCheckoutSession({ initiativeId, productCodes, returnUrl }: CheckoutSession): Promise<SubsriptionSession> {
    return this.post(`/initiatives/${initiativeId}/subscription/checkout-session`, {
      returnUrl,
      productCodes,
    }).then(response => response.data.data);
  }

  public async createCustomerPortal({ initiativeId, returnUrl }: CustomerPortalSession): Promise<SubsriptionSession> {
    return this.post(`/initiatives/${initiativeId}/subscription/customer-portal`, { returnUrl })
      .then(response => response.data.data);
  }

  /**
   * Adding here instead of mutation that cause redux state changes
   * @param initiativeId
   * @param returnUrl
   */
  public async addPaymentDetails({ initiativeId, returnUrl }: CustomerPortalSession): Promise<SubsriptionSession> {
    return this.post(`/initiatives/${initiativeId}/subscription/add-payment-details`, { returnUrl })
      .then(response => response.data.data);
  }

  public async userHasSurveys() {
    return this.get<{ data: { available: boolean } }>('/users/current/surveys/available')
      .then((result) => result.data.data);
  }

  public async loadRootInitiatives() {
    return this.get<{ data: RootInitiativeData[] }>('initiative-tree/companies')
      .then((result) => result.data.data);
  }

  public async getIdentityProviders(orgCode: string = '') {
    return this.get<{ data: IdentityProvider[] }>(`/auth/sso/${orgCode}`)
      .then((result) => result.data.data);
  }

  public async sendNotificationMessage(initiativeId: string, payload: MessagePayload) {
    return this.post(`/initiatives/${initiativeId}/notifications/send`, payload)
      .then(response => response.data.data)
  }

  public async getSystemLog(organizationId: string, params: object = {}) {
    return this.post(`/initiatives/${organizationId}/system-log`, params)
      .then((response) => response.data.data)
  }

  public async completeAssurancePortfolio(portfolioId: string, formData: AssurancePortfolioComplete, options?: AxiosRequestConfig) {
    return this.post(`/assurers/portfolios/${portfolioId}/complete`, formData, options)
      .then(response => response.data.data)
  }

  public async assureQuestions(portfolioId: string, data: { questions: string[] }) {
    return this.post(`/assurers/portfolios/${portfolioId}/assure-questions`, data)
      .then(response => response.data.data)
  }

  public async disputeQuestions(portfolioId: string, data: { questions: string[], text: string }) {
    return this.post(`/assurers/portfolios/${portfolioId}/dispute`, data)
      .then(response => response.data.data)
  }

  public async getAssuranceStakeholders(id: string): Promise<AssuranceStakeholdersData> {
    return this.get(`/assurers/portfolios/${id}/stakeholders`)
      .then((response) => response.data.data)
  }

  public async updateAssuranceStakeholders(id: string, data: UpdateStakeholderData): Promise<unknown> {
    return this.put(`/assurers/portfolios/${id}/stakeholders`, data)
      .then((response) => response.data.data)
  }

  public async getAssurancePermissions(id: string): Promise<AssurancePermissionsData> {
    return this.get(`/assurers/portfolios/${id}/permissions`)
      .then((response) => response.data.data)
  }

  public async addAssurancePortfolio(data: AssurancePortfolioCreate, options?: AxiosRequestConfig): Promise<{ _id: string }> {
    return this.post('/assurances/portfolio', flattenFiles(data), options)
      .then((response) => response.data.data)
  }

  public async updateAssurancePortfolio(data: AssurancePortfolioUpdate, options?: AxiosRequestConfig): Promise<{ _id: string }> {
    return this.patch(`/assurances/portfolio/${data.portfolioId}`, flattenFiles(data), options)
      .then((response) => response.data.data)
  }

  public async loadUniversalTracker(utrId: string): Promise<UniversalTrackerPlain> {
    return this.get(`universal-trackers/${utrId}`)
      .then((response) => response.data.data)
  }

  public async loadUniversalTrackerByCode(initiativeId: string, utrCode: string, period: DataPeriods): Promise<InitiativeUTRLookup> {
    return this.get(`/initiatives/${initiativeId}/universal-trackers/code/${encodeURIComponent(utrCode)}/${period}`)
      .then((response) => response.data.data)
  }

  public async getCustomReportValues(initiativeId: string, reportId: string): Promise<CustomReportsValue[]> {
    return this.get(`/initiatives/${initiativeId}/custom-reports/${reportId}/values`)
      .then(response => response.data.data)
  }

  public async customReportPreviewMetric(initiativeId: string, reportId: string, type: string, data: MetricSubmitData): Promise<{ type: 'string' | 'number', value: string | number }> {
    return this.post(`/initiatives/${initiativeId}/custom-reports/${reportId}/metrics/${type}/preview`, data)
      .then(response => response.data.data)
  }

  public async customReportDownloadExample(initiativeId: string, reportType: string) {
    return this.get(`initiatives/${initiativeId}/custom-reports/${reportType}/download-example`, { responseType: 'blob' }).then(this.downloadBlob);
  }

  public async getRequesterDataShare(lookupData: RequesterLookup): Promise<DataShareLookup> {
    const { requesterId, requesterType, initiativeId } = lookupData;
    return this.get(`/data-share/requester/${requesterType}/${requesterId}/initiative/${initiativeId}`)
      .then(response => response.data.data);
  }

  public async addSubsidiary(initiativeId: string, name: string) {
    return this.post(`initiatives/${initiativeId}/subsidiary`, { name }).then(response => response.data.data);
  }

  public async acceptUserAgreement(userId: string, agreementCode: UserAgreement): Promise<void> {
    return this.post(`/users/${userId}/agreements/${agreementCode}/accept`)
      .then(response => response.data.data);
  }

  public async acceptCompanyAgreement(initiativeId: string, agreementCode: string): Promise<void> {
    return this.post(`/initiatives/${initiativeId}/agreements/${agreementCode}/accept`)
      .then(response => response.data.data);
  }

  public async acceptCustomAgreement({ initiativeId, type, agreementCode }: AcceptCustomAgreementParams) {
    return this.post(`/initiatives/${initiativeId}/agreements/${type}/${agreementCode}/accept`)
      .then(response => response.data.data);
  }

  public async downloadUserStatsView(initiativeId: string, userId: string) {
    return this.get(`initiatives/${initiativeId}/stats/users/${userId}/export/excel`, { responseType: 'blob' }).then(this.downloadBlob);
  }

  public async downloadCustomReport(initiativeId: string, reportId: string) {
    return this.get(`/initiatives/${initiativeId}/custom-reports/${reportId}/download`, { responseType: 'blob' }).then(this.downloadBlob);
  }

  public async getIssuersByReferralCode(referralCode: string): Promise<{ issuers: IssuerListItem[], customCompanyEnabled?: boolean }> {
    return this.get(`/o/common/issuers/${referralCode}`)
      .then(response => response.data.data);
  }

  public async getIssuerByReferralCode(referralCode: string, issuerId: string): Promise<IssuerListItem> {
    return this.get(`/o/common/issuers/${referralCode}/${issuerId}`)
      .then(response => response.data.data);
  }
  public async sendOnboardingRequestToJoin(formData: RequestToJoinData, appOnboardingPath: string): Promise<boolean> {
    return this.post(`/direct/onboarding/user/${appOnboardingPath}/join`, formData)
      .then(response => response.data.data);
  }
  public async sendAuthenticatedRequestToJoin(formData: AuthenticatedRequestToJoinData, appOnboardingPath: string): Promise<boolean> {
    return this.post(`/direct/onboarding/${appOnboardingPath}/join`, formData)
      .then(response => response.data.data);
  }

  public async joinRequestAction(param: { initiativeId: string, action: string; onboardingId: string, roles?: UserRoles[] }) {
    return this.post(`/initiatives/${param.initiativeId}/onboarding`, param)
      .then(response => response.data.data);
  }

  public async getTemplateBlueprintQuestions(templateId: string): Promise<UniversalTrackerBlueprintMin[]> {
    return this.get(`/survey-templates/${templateId}/questions`)
      .then(r => r.data.data);
  }

  public async getBlueprintContributions(sourceName?: string): Promise<BlueprintContributions> {
    return this.get(`/blueprints/${sourceName}`)
      .then(response => response.data.data);
  }

  // AI responses

  public async getQuestionFurtherNotesDraft(initiativeId: string, data: {
    utrvId: string,
    draftData: Pick<UniversalTrackerValuePlain, 'value' | 'unit' | 'numberScale' | 'valueData'>
  }): Promise<AIResponse> {
    return this.post(`/initiatives/${initiativeId}/ai/generate-draft/further-notes`, data)
      .then(response => response.data.data);
  }

  public async getQuestionInputFieldDraft(data: {
    utrId: string,
  }): Promise<AIResponse> {
    return this.post('/o/common/ai/generate-draft/input-text', data)
      .then(response => response.data.data);
  }

  public async getSurveyQuestionAndAnswer(initiativeId: string, surveyId: string, question: string): Promise<AIResponse> {
    return this.post(`/initiatives/${initiativeId}/ai/survey/${surveyId}/q-and-a`, { question })
      .then(response => response.data.data);
  }

  public async getSurveyNarrative(initiativeId: string, surveyId: string): Promise<AIResponse> {
    return this.post(`/initiatives/${initiativeId}/ai/survey/${surveyId}/sdg-summary`)
      .then(response => response.data.data);
  }
}

const G17Client = new G17Api({ baseURL: config.apiUrl }, OktaStorage);

export default G17Client;
