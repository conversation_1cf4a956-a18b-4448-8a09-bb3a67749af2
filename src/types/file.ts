/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

export type FileTypes = 'file' | 'link';

export interface FileWithPreview extends File {
  preview?: string;
  Latitude?: string | number;
  Longitude?: string | number;
  exifdata?: {
    GPSLongitudeRef: string;
    GPSLatitudeRef: string;
    GPSLongitude: number[];
    GPSLatitude: number[];
  };
  path?: string;
  saveToLibrary?: boolean;
}

export interface NewEvidenceFile {
  description: string;
  title: string;
  file: FileWithPreview;
  type: FileTypes;
  link?: string;
  saveToLibrary?: boolean;
}

export type TKeyValue = {
  [key: string]: any;
};

export interface UpdateDescriptionParams {
  path?: string;
  description: string;
  isUpdate: boolean;
}

export type HandleFileDescriptionFn = (params: UpdateDescriptionParams) => void;
