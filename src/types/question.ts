import { TableDataInfo } from '@components/survey/question/questionInterfaces';
import { Variation } from './universalTracker';
import { ValueData } from '@g17eco/types/surveyScope';

export type CurrentInputData = {
  value?: number;
  table: TableDataInfo;
  valueData: ValueData;
  unit?: string;
  numberScale?: string;
};

export interface UtrvVariation extends Variation {
  valueListCode?: string; // table column's code or numeric value list's code
  details: {
    baseline: number;
    min: number;
    max: number;
    unit?: string;
    numberScale?: string;
    effectiveDate: Date;
  };
}

export interface UtrvVariationWarning {
  valueListCode?: string;
  formattedBaseline: string;
  baselineReportingDate: string;
  formattedCurrentInput: string;
  variance: number;
  confirmationRequired: boolean;
}
