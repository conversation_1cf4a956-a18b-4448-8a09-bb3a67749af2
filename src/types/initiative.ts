/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { UniversalTrackerValuePlain } from './surveyScope';
import { DataPeriods, UniversalTrackerPlain } from './universalTracker';
import { DataShare, DataShareMin } from './dataShare';
import { MonthDay } from './onboarding';
import { UserRoles } from '../constants/user';
import { SurveyModelMinData } from '../model/surveyData';
import { AppConfig } from './app';
import { BankingSetting } from './banking-settings';
import { FeatureDetails, FeatureTag, RequiredTags } from '@g17eco/core';
import { Sponsorship } from '@g17eco/types/sponsorship';

export enum RequestReviewMethod {
  Review = 'review',
  Automate = 'automate',
  NoAccess = 'noAccess',
}

export type InitiativeDataShare = Pick<DataShare, 'dataScope'> & { requestReview?: RequestReviewMethod };

export interface InitiativeMin {
  _id: string;
  code: string;
  name: string;
  profile?: string;
  permissionGroup?: string;
  tags?: string[];
  parentId?: string;
  dataShare?: InitiativeDataShare;
}

export interface InitiativeMinPlain {
  _id: string,
  code: string,
  name: string,
  profile?: string,
  type?: InitiativeType,
  industryText?: string;
  startDate?: string,
  endDate?: string,
  industry?: Industry;
  parentId?: string;
  dataShare?: InitiativeDataShare;
}

export type SubscriptionStatus =
  // Fine
  | 'active'
  | 'trialing'

  // Not recoverable
  | 'canceled'
  | 'incomplete_expired'

  // Need user interaction
  | 'past_due'
  | 'unpaid'
  | 'incomplete'

export interface SubscriptionItem {
  id: string;
  priceId: string;
  productId: string;
  productCode?: string;
  created: number;
}

interface Coupon {
  id: string;
  currency: string | null;
}

interface Discount {
  id: string;
  coupon?: Coupon;
  start: number;
  end: number | null;
}

export interface Subscription {
  id: string;
  status: SubscriptionStatus;
  // List of subscription items, each with an attached price.
  items: SubscriptionItem[];
  startDate: number;
  endDate?: number;
  cancelDate?: number;
  periodStart: number;
  periodEnd: number;
  trialStart?: number;
  trialEnd?: number;
  paymentProvider?: 'stripe' | 'wwg';
  discount?: Discount;
}

// Represent stripe customer
export interface Customer {
  id: string;
  defaultPaymentMethod?: string;
  subscriptions: Subscription[];
}

export interface IndustryLevels {
  [key: string]: string;
  level1: string;
  level2: string;
  level3: string;
  level4: string;
}

export interface Industry {
  [key: string]: IndustryLevels;
  gics: IndustryLevels;
  icb: IndustryLevels;
  icb2019: IndustryLevels;
}

export enum InitiativeType {
  initiative = 'initiative',
  initiativeGroup = 'initiativeGroup'
}

export enum InitiativeTags {
  StaffOrganization = 'staff_organization',
  VerifiedOrganization = 'verified_organization',
  DemoOrganization = 'demo_organization',
  Organization = 'organization',
  Country = 'country',
}

export interface Group {
  initiativeId: string,
  weight: number
}

export interface InitiativeGroup {
  code: string;
  name: string;
  group: Group[];
  created?: string;
}

export enum Materiality {
  Low = 'low',
  Medium = 'medium',
  High = 'high',
  None = 'none',
}

export interface MaterialityMap {
  [Materiality.Low]: string[];
  [Materiality.Medium]: string[];
  [Materiality.High]: string[];
  [Materiality.None]: string[];
}

export const MATERIALITY_THRESHOLDS_MAP: Record<Materiality, number> = {
  [Materiality.High]: 100,
  [Materiality.Medium]: 66,
  [Materiality.Low]: 33,
  [Materiality.None]: 0,
};

export interface InitiativeFile {
  _id: string;
  title: string;
  description: string;
  ownerId: string;
  url: string;
  size: number;
  created: Date;
  metadata: {
    name: string;
    mimetype: string;
    extension: string;
  }
}

interface DisplayItem {
  view: string;
}

export interface DisplaySettings {
  targetActual?: DisplayItem;
  sdgContributionChart?: {
    liveSharing: boolean;
    token?: string;
  }
}

export interface Referral {
  code?: string;
  referrer?: string;
  usedDate?: string;
}

export type CompanyTagType = InitiativeTags | FeatureTag | RequiredTags;

export interface InitiativePlain extends InitiativeMin {
  type?: InitiativeType;
  materiality?: {
    [key: string]: number
  };
  initiativeGroupId?: string;
  materialityMap?: MaterialityMap;
  description?: string;
  missionStatement?: string;
  permissionGroup?: string;
  created: string;
  startDate?: string;
  endDate?: string;
  usage: string[];
  parentId?: string;
  customer?: Customer;
  tags?: CompanyTagType[];
  referrals?: Referral[];
  appConfigCode?: string;
  initiativeGroup?: InitiativeGroup;
  metadata?: {
    agreements?: {
      [key: string]: {
        date: string;
        userId: string;
      }
    }
  }
}

export interface LinkedUniversalTracker {
  universalTrackerId: string,
  usage: string[],
}

export interface InitiativeData extends InitiativeMinPlain {
  created: string;
  customer?: Customer;
  requestedDataShares?: DataShareMin[];
  description?: string;
  displaySettings?: DisplaySettings;
  files: InitiativeFile[];
  geoLocation?: string;
  initiativeGroupId: string; /** should be optional as only available for portfolios? */
  isPublic: boolean;
  lastCompletedSurveyDate: Date;
  lastSurveyDate: Date;
  linkedUniversalTrackers: LinkedUniversalTracker[];
  materiality?: {
    [key: number]: number
  };
  missionStatement?: string;
  permissionGroup: string;
  profile: string;
  referrals?: Referral[];
  rations: {
    code: string,
    rating: string;
  }[];
  tags: InitiativeTags[];
  usage: string[];
  userCount: number;
  userPermissions: {
    initiativeId: string,
    permissions: UserRoles[]
  }
  root?: InitiativePlain
  visible: boolean;
  financialEndDate?: MonthDay;
  sectorText: string;
  country?: string;
  bankingSettings?: BankingSetting[];
  appConfigCode?: string;
}

export interface PeerComparison {
  current: { _id: string; value?: number };
  peers: any[];
  min: number;
  max: number;
  avg: number;
}

export interface InitiativeRating {
  _id?: string;
  code: string;
  rating: string;
  link?: string;
  linkText?: string;
  date?: string;
}

type ReferenceUtrv = Pick<UniversalTrackerValuePlain, '_id' | 'effectiveDate'>;

export interface InitiativeUTRLookup {
  utr: UniversalTrackerPlain;
  latestSurveyId: string | undefined;
  utrv: ReferenceUtrv | undefined;
}

export interface RatingAgency {
  code: string;
  title: string;
  logoSrc: string;
  ratingAgencyUrl: string;
  additionalInformation: string;
}

export interface RatingAgencyRating extends RatingAgency, InitiativeRating {
}

interface ExchangeStats {
  private: number;
  na: number;
  nr: number;
  answered: number;
  total: number;
}

export interface ExchangeSurvey extends ExchangeStats {
  _id: string;
  name: string;
  completedDate: string;
  effectiveDate: string;
  period: DataPeriods;
  [key: string]: any;
}

export interface InitiativeCompany extends InitiativePlain {
  userCount: number;
  lastSurveyDate?: Date;
  lastCompletedSurveyDate?: Date;
  requestedDataShares?: DataShareMin[];
  sectorText?: string,
  industryText?: string,
  exchangeSurveys?: ExchangeSurvey[];
}

export interface InitiativeBenchmarking extends InitiativePlain {
  sectorText?: string,
  industryText?: string,
  benchmarkingSurveys?: ExchangeSurvey[];
  latestSurvey?: ExchangeSurvey;
}

export interface RootInitiativeData extends Pick<InitiativePlain,
  '_id' | 'name' | 'tags' | 'type' | 'permissionGroup' | 'parentId' | 'appConfigCode' | 'created' | 'metadata' | 'referrals'> {
  firstInitiativeId?: string;
  appConfig?: AppConfig;
  customer?: Pick<Customer, 'defaultPaymentMethod'>;
  calculatedSubscriptions: Subscription[];
}

export interface CustomScope {
  scopeType: string,
  code: string,
  required: boolean,
}

export interface DomainConfig {
  subdomain: string,
  portfolioTrackerCode: string,
  scope: CustomScope[]
}

export interface RootConfig {
  features?: FeatureDetails[];
  defaultFeatures?: FeatureDetails[];
  survey: Pick<DomainConfig, 'scope'>;
}

export type EditOptionAccessorType = keyof Pick<
  InitiativeData,
  'name' | 'description' | 'missionStatement' | 'geoLocation' | 'industry'
>;

export interface EditOptionType {
  label: string;
  type: string;
  accessor?: EditOptionAccessorType;
  required?: boolean;
}

export interface EditOptionProps extends EditOptionType {
  rootOrg: RootInitiativeData;
  initiative: InitiativeData;
  onClick?: () => void;
}

type SurveyConfigProps = Required<
  Pick<SurveyModelMinData, 'evidenceRequired' | 'verificationRequired' | 'noteRequired' | 'isPrivate' | 'unitConfig'>
> & Partial<Pick<SurveyModelMinData, 'noteInstructions' | 'noteInstructionsEditorState'>>;

export interface DefaultSurveyConfig extends SurveyConfigProps {
  subsidiariesEnforced?: boolean;
}

export interface SponsoredInitiative {
  _id: string;
  name: string;
  customerId: string;
  canBeSponsored: boolean;
  subscription: Subscription;
  sponsorship?: Sponsorship;
}

export type SafeInitiativeFields = Pick<
  InitiativeData,
  '_id' | 'profile' | 'name' | 'sectorText' | 'industryText' | 'description' | 'missionStatement'
>;
