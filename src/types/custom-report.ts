/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { DownloadUtrvStatus } from '../components/downloads/util/downloadReportHandler';
import { SurveyType } from './survey';
import { UniversalTrackerValuePlain } from './surveyScope';
import { DataPeriods, UniversalTrackerPlain } from './universalTracker';
import { SURVEY } from '@constants/terminology';
import { Scope } from '@models/surveyData';
import { OrderingDirection } from './common';
import { CustomReportTemplateType } from '../apps/company-tracker/components/custom-report/template-builder/types';
import { StringIdTableNode } from '@components/downloads/util/treeNavigation';

export enum CustomReportType {
  Metrics = 'metrics',
  Initiatives = 'initiatives',
  Survey = 'survey',
  /** @deprecated it was used only for filtering on FE that is no longer the case */
  SurveyAggregation = 'survey_aggregation',
  Template = 'template',
}

export type DateReportType = CustomReportType.Survey | CustomReportType.SurveyAggregation;

export type SurveyData = {
  initiativeIds: string[];
  statuses?: string[];
  assuranceStatus?: string[];
  surveyFilters: SurveyFilter[];
};

export type SurveyDataRange = {
  // Aggregation is restricted to 1 surveyId
  ids: string[];
  statuses?: string[];
  assuranceStatus?: string[];
};

export type SurveyFilter = {
  effectiveDate: string;
  period: DataPeriods;
  type: SurveyType;
};

export type SurveyTemplate = {
  initiativeIds: string[];
  statuses: string[];
  assuranceStatus?: string[];
  visibility: string;
  scope: Scope;
  displayMetricOverrides?: boolean;
  displayUserInput?: boolean;
  surveyFilters: SurveyFilter[];
};

export const TABULAR_COLUMN_CODES = [
  'standard',
  'code',
  'mapping',
  'valueType',
  'name',
  'valueLabel',
  'unit',
  'numberScale',
  'input',
  'note',
  'tags',
  'provenance',
  'instructions',
  'status',
  'assuranceStatus',
  'updatedDate',
  'effectiveDate',
  'subsidiaryCode',
  'subsidiaryName',
  'subsidiaryHierarchy',
  'surveyName',
] as const;

export const COLUMN_CODES = [...TABULAR_COLUMN_CODES, 'columnLabel'] as const;

export const TRANSPOSED_COLUMN_CODES = [...COLUMN_CODES, 'row', 'aggregationGroupBy'] as const;

export type ColumnCode =
  | (typeof TABULAR_COLUMN_CODES)[number]
  | (typeof COLUMN_CODES)[number]
  | (typeof TRANSPOSED_COLUMN_CODES)[number];

export enum InputColumnRule {
  Latest = 'latest',
  All = 'all',
}

interface BaseColumn {
  code: ColumnCode;
  header?: string;
}

interface InputColumn extends BaseColumn {
  code: 'input';
  rule?: InputColumnRule;
}

export enum AggregationGroupByRule {
  ExcludeHeading = 'exclude',
  IncludeHeading = 'include',
}

interface AggregationGroupByColumn extends BaseColumn {
  code: 'aggregationGroupBy';
  rule?: AggregationGroupByRule;
}

export type Column = BaseColumn | InputColumn | AggregationGroupByColumn;

export function isInputColumn(column: Column): column is InputColumn {
  return column.code === 'input';
}

export function isAggregationGroupByColumn(column: Column): column is AggregationGroupByColumn {
  return column.code === 'aggregationGroupBy';
}

/*
  For now, this type does not affect the way we order the records in custom report.
  In both cases, user can choose 1 column only and Ordering['columns'] will contains only 1 element but
  we need the type to know that user select the custom option or one of the column options from ordering dropdown in FE.
*/
export enum OrderingType {
  Default = 'default', // Ordering['columns'] contains only 1 element
  Custom = 'custom', // Ordering['columns'] contains 1 or more elements
}

export interface OrderingColumn {
  code: ColumnCode;
  direction: OrderingDirection;
}

export interface Ordering {
  type: OrderingType;
  columns: OrderingColumn[];
}

export interface Config {
  columns: Column[];
  ordering?: Ordering;
  templateType?: CustomReportTemplateType;
}

export interface NewCustomReport {
  name: string;
  description?: string;
  status?: DownloadUtrvStatus;
  assuranceStatus?: DownloadUtrvStatus;
}

export interface NewMetrics extends NewCustomReport {
  type: CustomReportType.Metrics;
}

export interface NewInitiativesOrSurvey extends NewCustomReport {
  type: CustomReportType.Initiatives | CustomReportType.Survey;
  survey: SurveyData;
}

export interface NewDateComparisonReport extends NewCustomReport {
  type: DateReportType;
  survey: SurveyDataRange;
}

export interface NewCustomReportTemplate extends Pick<NewCustomReport, 'name' | 'description'> {
  type: CustomReportType.Template;
  survey: SurveyTemplate;
  config?: Config;
}

export type CustomReportFormData =
  | NewMetrics
  | NewInitiativesOrSurvey
  | NewDateComparisonReport
  | NewCustomReportTemplate;

export interface CustomReport extends NewCustomReport {
  _id: string;
  initiativeId: string;
  type?: CustomReportType;
  created: string;
  lastUpdated: string;
  metrics: CustomReportMetric[];
  universalTrackers?: UniversalTrackerPlain[];
  survey?: SurveyData | SurveyDataRange | SurveyTemplate;
}

export interface CustomInitiatives extends CustomReport {
  type: CustomReportType.Initiatives;
  survey: SurveyData;
}
export interface SurveyRange extends CustomReport {
  type: CustomReportType.Survey | CustomReportType.SurveyAggregation;
  survey: SurveyDataRange;
}

export interface CustomReportTemplate extends CustomReport {
  type: CustomReportType.Template;
  survey: SurveyTemplate;
  config?: Config;
}

export interface CustomReportMetric {
  utrId: string;
  type: MetricType;
}

export enum MetricType {
  Single = 'single',
  Calculated = 'calculated',
  Text = 'text',
}

const metricNames: { [key: string]: string } = {
  single: 'Single metric',
  calculated: 'Total or % calculation',
  text: 'Collection of text',
};

export const SURVEY_TYPE_LABELS: { [key in SurveyType]: string } = {
  [SurveyType.Default]: SURVEY.CAPITALIZED_PLURAL,
  [SurveyType.Aggregation]: `Combined ${SURVEY.PLURAL}`,
  [SurveyType.Materiality]: `Materiality ${SURVEY.PLURAL}`,
  [SurveyType.AutoAggregation]: `Auto-aggregated ${SURVEY.PLURAL}`,
};

export const getMetricTypeLabel = (metricType: MetricType) => metricNames[metricType] ?? 'Unknown metric type';

export type ShowAsTypes = 'total' | 'percentage' | 'text';

export interface MetricSubmitData {
  name: string;
  showAs: ShowAsTypes;
  percentageOf?: 'count' | 'metric';
  primaryUtrCode: string;
  primaryValueListCode: string;
  primaryColumnCode?: string;
  secondaryUtrCode?: string;
  secondaryValueListCode?: string;
  secondaryColumnCode?: string;
}

export interface CustomReportsValue {
  utr: UniversalTrackerPlain;
  utrv: UniversalTrackerValuePlain;
  customMetricType: MetricType;
}

export interface CustomReportSubsidiaryStats {
  _id: string;
  name: string;
  parentId?: string;
  surveyCount: number;
}

export interface CustomReportSubsidiarySurvey {
  _id: string;
  effectiveDate: string;
  period: DataPeriods;
  type: SurveyType;
  scope?: Scope;
  initiativeId: string;
}

export interface DateCustomReportData {
  _id: string;
  effectiveDate: string;
  type: SurveyType;
  surveyName: string;
  period: DataPeriods;
  status: {
    created: number;
    updated: number;
    rejected: number;
    verified: number;
  };
}

export interface TableNodeWithSurveyData extends StringIdTableNode {
  _id: string;
  name: string;
  surveyName: string;
  type: SurveyType;
  effectiveDate: string;
  status: {
    created: number;
    updated: number;
    rejected: number;
    verified: number;
  };
}

export enum CreateCustomReportType {
  Calculated = 'calculated',
  Subsidiary = 'subsidiary',
  Date = 'date',
  Template = 'template',
}

export interface CustomReportProps<T = CustomReport> {
  initiativeId: string;
  customReport?: T;
}
