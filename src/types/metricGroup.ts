/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { OrderingDirection } from './common';
import { InitiativePlain } from './initiative';
import { UniversalTrackerPlain } from './universalTracker';

export enum AccessType {
  Custom = 'custom',
  Assigned = 'assigned',
  Inherited = 'inherited',
}

export interface MetricGroupShare {
  initiativeId: string;
  created: Date;
  acceptedDate?: Date;
  initiative?: InitiativePlain;
}

export enum CustomMetricOrderType {
  Name = 'name',
  TypeCode = 'typeCode',
  Custom = 'custom',
}

export interface CustomMetricsOrder {
  orderType: CustomMetricOrderType;
  // Used for non-custom order
  direction?: OrderingDirection;
}

export interface MetricGroup  {
  _id: string;
  groupName: string;
  description?: string;
  initiativeId: string;
  initiative?: InitiativePlain;
  universalTracker?: UniversalTrackerPlain[];
  universalTrackers?: string[];
  groupData?: {
    colour?: string,
    link?: string,
    icon?: string
    preferredAltCodes?: string[];
  };
  metricsOrder?: CustomMetricsOrder;
  share?: MetricGroupShare[];
  accessType?: AccessType;
  createdBy?: string;
  created?: string;
}

export interface ImportMetricGroupParams {
  initiativeId: string,
  groupId: string,
  replace?: boolean;
  regenerate?: boolean;
  data: { QuestionCode: string, name?: string }[],
  mapping?: Record<string, string>
}

export enum MetricGroupType {
  Custom = 'custom',
  Tag = 'tag',
}

export type Tag = Pick<MetricGroup, '_id' | 'initiativeId' | 'universalTrackers' | 'groupName'>;

export interface CustomMetricsUsage {
  organisationCurrentUsage: number;
  organisationLimit: number;
  subsidiaryCurrentUsage: number;
}
