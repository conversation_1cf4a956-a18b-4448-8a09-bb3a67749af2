/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import { OrderingDirection } from './common';
import { InitiativePlain } from './initiative';
import { UniversalTrackerPlain } from './universalTracker';
import { MaterialitySurveyModelMinData } from '@apps/materiality-tracker/api/materiality-assessment';

export enum AccessType {
  Custom = 'custom',
  Assigned = 'assigned',
  Inherited = 'inherited',
}

export interface MetricGroupShare {
  initiativeId: string;
  created: Date;
  acceptedDate?: Date;
  initiative?: InitiativePlain;
}

export enum CustomMetricOrderType {
  Name = 'name',
  TypeCode = 'typeCode',
  Custom = 'custom',
}

export interface CustomMetricsOrder {
  orderType: CustomMetricOrderType;
  // Used for non-custom order
  direction?: OrderingDirection;
}

export enum MetricGroupSourceType {
  Survey = 'survey',
}

interface MetricGroupSurveySource {
  type: MetricGroupSourceType.Survey;
  surveyId: string;
  jobId: string;
  topTopicsCount: number;
  topicUtrs: { _id: string }[];
}

type MetricGroupSource = MetricGroupSurveySource;

export interface MetricGroup {
  _id: string;
  groupName: string;
  description?: string;
  initiativeId: string;
  initiative?: Pick<InitiativePlain, '_id' | 'name'>;
  universalTracker?: UniversalTrackerPlain[];
  universalTrackers?: string[];
  groupData?: {
    colour?: string,
    link?: string,
    icon?: string
    preferredAltCodes?: string[];
  };
  survey?: Pick<MaterialitySurveyModelMinData, '_id' | 'assessmentType' | 'completedDate' | 'effectiveDate'>;
  source?: MetricGroupSource;
  metricsOrder?: CustomMetricsOrder;
  share?: MetricGroupShare[];
  accessType?: AccessType;
  createdBy?: string;
  created?: string;
  updated: string;
}

export interface ImportMetricGroupParams {
  initiativeId: string,
  groupId: string,
  replace?: boolean;
  regenerate?: boolean;
  data: { QuestionCode: string, name?: string }[],
  mapping?: Record<string, string>
}

export enum MetricGroupType {
  Custom = 'custom',
  Tag = 'tag',
}

export type Tag = Pick<MetricGroup, '_id' | 'initiativeId' | 'universalTrackers' | 'groupName'>;

export interface CustomMetricsUsage {
  organisationCurrentUsage: number;
  organisationLimit: number;
  subsidiaryCurrentUsage: number;
}
