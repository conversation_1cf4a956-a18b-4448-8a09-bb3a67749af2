/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import { ScopeQuestion, ScopeQuestionOptionalValue, UniversalTrackerValuePlain } from './surveyScope';
import { AggregationMode, DataPeriods, UniversalTrackerPlain } from './universalTracker';
import { InitiativeMinPlain } from './initiative';
import { Scorecard } from './scorecard';
import { GroupData, Scope, ScopeGroups, SurveyModelMinData, UnitConfig } from '../model/surveyData';
import { CardGridViewMode } from '../components/survey-scope/CardGrid';
import { Group, Standards } from '@g17eco/core';
import { TransformMapping } from '../utils/file/columnMapping';
import { SurveyUserRoles } from '../constants/users';
import { CustomMetricsOrder } from './metricGroup';
import { UtrvFilter } from './insight-custom-dashboard';

export type SurveyListItem = Pick<SurveyModelMinData,
  | '_id'
  | 'name'
  | 'scope'
  | 'completedDate'
  | 'type'
  | 'period'
  | 'effectiveDate'
>

export interface SurveyInitiative {
  _id: string;
  initiativeId: string;
  initiative: InitiativeMinPlain;
  effectiveDate: string;
  name?: string;
  completedDate?: string;
  aggregatedDate?: string;
  aggregatedVersion?: number;
  aggregatedVersionMismatch?: boolean;
  period?: DataPeriods;
  type?: SurveyType;
  sourceName?: string;
  unitConfig?: UnitConfig;
}

export enum SurveyType {
  Default = 'default',
  Aggregation = 'aggregation',
  Materiality = 'materiality',
  AutoAggregation = 'auto_aggregation',
}

export interface SurveySummary extends SurveyInitiative {
  status: {
    created: number,
    updated: number,
    rejected: number,
    verified: number,
    assured?: number
  },
  period?: DataPeriods;
  scorecard: Scorecard;
  assurance: {
    status: string;
  }[];
}

export interface CSVFormat {
  QuestionCode: string;
  Question?: string;
  Value: string;
  Comment?: string;
  OptionCode?: string;
}

export interface DataImportRow extends CSVFormat {
  skipped?: boolean;
  utr?: UniversalTrackerPlain;
  utrv?: UniversalTrackerValuePlain;
}

export interface SurveyAddAssurance {
  _id: string;
  initiativeId: string;
}

export interface MassDelegation {
  role: SurveyUserRoles | SurveyUserRoles[];
  utrvIds: string[];
  userId?: string | string[];
}

export interface BlueprintContributions {
  [k: string]: string[];
}

export interface QuestionList<T extends ScopeQuestionOptionalValue = ScopeQuestion> extends Pick<Group, 'code' | 'name'>{
  list: T[];
}

export interface BaseQuestionGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> {
  alwaysVisible?: boolean;
  groupName: string;
  groupData?: GroupData;
  groupCode?: string;
  groupId?: string;
  count?: number;
  list: T[];
  subGroups?: QuestionList<T>[];
}

interface SurveyQuestionBase {
  utr: UniversalTrackerPlain;
}

export interface QuestionGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> extends BaseQuestionGroup<T> {
  questions: SurveyQuestionBase[],
}

// Alias
export type ScopeQuestionGroupOptionalValue = BaseQuestionGroup<ScopeQuestionOptionalValue>;
export type ScopeQuestionGroup = BaseQuestionGroup<ScopeQuestion>;

export interface ScopeGroupData {
  title: string;
  subtitle?: string;
  description?: string;
  src?: string;
  link?: string;
  iconClassName?: string;
  colour?: string;
  preferredAltCodes?: string[];
  // Used for sorting utrs in metric groups
  metricsOrder?: CustomMetricsOrder;
  universalTrackerIds?: string[];
  isInherited?: boolean;
}

export interface ScopeGroup<T extends ScopeQuestionOptionalValue = ScopeQuestion> {
  group: ScopeGroupData;
  list: T[];
}

export interface SurveySettings {
  groupBy: string[];
  viewLayout: CardGridViewMode;
  filterByStatus: string[];
  filterByDelegationStatus: string[];
  filterByMateriality: string[];
  filterByGoal: string[];
  filterByRegulatory: string[];
  filterByModules: string[];
  filterByRole: string[];
  filterByUser: string[];
  filterByTag: string[];
  searchText: string;
  filterByBookmarks: boolean;
}

export type ScopeCategoryGroups<T extends ScopeQuestionOptionalValue = ScopeQuestion> = {
  [key in ScopeGroups]: {
    [key: keyof Standards]: ScopeGroup<T>
  }
};

export interface SurveyImport {
  file: File;
  mapping: TransformMapping<string>;
  tabName: string;
}
export interface BulkSurveyImport {
  initiativeId: string;
  file: File;
}

export type AllowedAggregatedUtrvStatus = UtrvFilter.AllAnswered | UtrvFilter.Verified;
export interface AggregatedSurveyFilters {
  utrv: AllowedAggregatedUtrvStatus | undefined;
}

export interface CreateCombineSurvey {
  name: string,
  surveyIds: string[];
  period?: DataPeriods;
  scope?: Scope;
  aggregationMode?: AggregationMode;
  filters: AggregatedSurveyFilters;
}
