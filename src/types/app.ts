/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */


import { DisplayOption, DownloadUtrvStatusCombined } from '@components/downloads/util/downloadReportHandler';
import { CompanyAgreement, UserAgreement } from '../reducers/current-user';
import { ProductCodes } from '../services/SubscriptionService';
import { SurveyOverviewMode } from '../slice/surveySettings';
import { OnboardingStep } from './onboarding';
import { FeatureTag } from '@g17eco/core';


export enum DescriptionType {
  ExternalLink = 'external_link',
  HyGraph = 'hygraph',
  Text = 'text',
}


interface GraphCms {
  type: DescriptionType.HyGraph,
  code: string
  text: string;
}

interface ExternalLink {
  type: DescriptionType.ExternalLink,
  text: string;
  url: string
}

interface TextNode {
  type: DescriptionType.Text,
  text: string
}

export type DescriptionElement = TextNode | ExternalLink | GraphCms;

/** Represent Agreement modal data **/
interface AgreementData {
  title: string;
  description: DescriptionElement[];
}



export interface AgreementConfig<T extends string = string> {
  code: T;

  /** These apply for initiative created Date **/
  fromDate?: Date;

  /** These only take active from specific date, therefore they can be scheduled **/
  startDate?: Date;

  /**
   * It will no longer be required or active after end date
   * Most of the time it should be replaced by newer version of agreement
   */
  endDate?: Date;

  /**
   * Custom rendering data
   * Mostly to populate custom agreement modal, not the actual agreement that
   * ideally will live outside the platform.
   */
  data?: AgreementData
}

export interface CustomAgreementConfig extends AgreementConfig {
  type?: 'company' | 'sponsorship';
}

export enum AppCode {
  WWG = 'wwg',
  TOLI = 'toli',
  SGXESGenome = 'sgx_esgenome',
  CompanyTrackerStarter = 'company_tracker_starter',
  CompanyTracker = 'company_tracker_light',
  CompanyTrackerPro = 'company_tracker_pro',
  CompanyTrackerEnterprise = 'company_tracker_enterprise',
  PortfolioTrackerExchange = 'portfolio_tracker_exchange',
  PortfolioTrackerExchangeSGX = 'portfolio_tracker_exchange_sgx',
  MaterialityTracker = 'materiality_assessment',
}

export interface AppConfig {
  code: AppCode;
  productCode: ProductCodes;
  validProductCodes: ProductCodes[];

  // App Setup
  name: string;
  logo: string;
  rootAppPath: string;
  onboardingPath: string;

  /** Overall website change **/
  whitelabel?: {
    logo?: string;
  };
  /** App specific changes **/
  settings: {
    defaultSurveyOverviewMode?: SurveyOverviewMode;

    /**
     * Used by question progress circles to determine what to display or "add" to scope
     * Note: used to be only used when company is CTL, now is respected.
     */
    overviewRecommendedAddons: string[];

    /**
     * Determine what appears at the top of scope view (core packs)
     * Also used the preferred alternative codes for SDG Contributions
     * detail view, question list and insight references etc.
     * The order is important
     **/
    settingsRecommendedAddons: string[];

    /**
     * All of these codes will be split to free and premium groups and displayed
     * as option to add to the scope
     */
    availableAddons?: string[];
    requiresTermsAndConditions?: boolean;
    onboardingSteps?: OnboardingStep[];
    userAgreementsRequired?: AgreementConfig<UserAgreement>[];
    companyAgreementsRequired?: AgreementConfig<CompanyAgreement>[];
    canViewAllPacks?: boolean;
    canEditInsightOverview?: boolean;
    canShowSDGDetails?: boolean;
    defaultDownloadOptions?: {
      metricStatuses?: DownloadUtrvStatusCombined[];
      metricOverrides?: DisplayOption[];
    };
    betaFeatures?: FeatureTag[];
  };
}
