import { ROUTES } from '@constants/routes';
import { InsightPage } from '@routes/summary/insights/utils/constants';
import { getValidPage, getInsightPageOptions, isInsightLayoutPage } from '@routes/summary/insights/utils/helpers';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';
import { generateUrl } from '@routes/util';
import { useHistory } from 'react-router-dom';
import { InsightDashboard } from '@g17eco/types/insight-custom-dashboard';

interface Props {
  initiativeId: string;
  portfolioId: string;
  summaryPage: InsightPage;
  dashboardId?: string;
  dashboards: Pick<InsightDashboard, '_id' | 'title'>[];
}

export const usePTCompanyCustomDashboards = ({
  initiativeId,
  portfolioId,
  summaryPage: summaryPageParam = InsightPage.Overview,
  dashboardId,
  dashboards,
}: Props) => {
  const history = useHistory();
  const summaryPage = isInsightLayoutPage(summaryPageParam) ? summaryPageParam : undefined;

  const handleNavigate = (page: string) => {
    const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY, {
      portfolioId,
      companyId: initiativeId,
      summaryPage: page,
    });
    history.push(url);
  };

  const handleNavigateCustom = (dashboardId: string) => {
    const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY_DASHBOARD, {
      portfolioId,
      companyId: initiativeId,
      dashboardId,
    });
    history.push(url);
  }

  const handleClickOption = (item: InsightDashboardOption) => {
    if (item.disabled) {
      return;
    }
    return item.isCustom ? handleNavigateCustom(item.value) : handleNavigate(item.value);
  };

  const insightPageOptions: InsightDashboardOption[] = getInsightPageOptions();

  const dashboardOptions: InsightDashboardOption[] = dashboards.map((dashboard) => ({
    value: dashboard._id,
    label: dashboard.title,
    isCustom: true,
    disabled: false,
    isSharedByParent: false,
  }));

  return {
    currentPage: dashboardId || getValidPage(summaryPage),
    options: [...insightPageOptions, ...dashboardOptions],
    handleNavigate,
    handleClickOption,
  };
};
