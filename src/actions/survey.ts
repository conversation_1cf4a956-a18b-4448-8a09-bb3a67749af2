/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import * as actionTypes from '../constants/action-types';
import { handleTokenAuth } from './api';
import { handleError } from './error';
import { reloadSurveyList } from '../slice/initiativeSurveyListSlice';
import { AppDispatch, RootState } from '../reducers';
import { ThunkAction } from 'redux-thunk';
import { AnyAction } from 'redux';
import { SurveyActionData } from '../model/surveyData';
import { ConfigFormData } from '@g17eco/molecules/form';
import G17Client from '../services/G17Client';
import { addSiteAlert, SiteAlertColors } from '../slice/siteAlertsSlice';
import { SURVEY } from '@constants/terminology';

type Thunk = ThunkAction<void, RootState, any, AnyAction>;

const handleSurveyAction = (dispatch: AppDispatch) => (promise: Promise<any>) => {
  return promise
    .then(response => {
      dispatch({
        type: actionTypes.LOADED_SURVEY_ACTIONS,
        data: response.data.data,
      });
    })
    .catch(handleError(dispatch, actionTypes.ERROR_SURVEY_ACTIONS));
};

export const handleUnloadSurvey = () => (dispatch: AppDispatch) => {
  dispatch({ type: actionTypes.UNLOAD_SURVEY_ACTIONS });
};

export const completeSurvey = async (surveyId: string): Promise<void> => {
  return G17Client.patch(`/surveys/${surveyId}/complete`)
    .then(response => response.data.data)
};

export const uncompleteSurvey = async (surveyId: string): Promise<void> => {
  return G17Client.delete(`/surveys/${surveyId}/complete`)
    .then(response => response.data.data)
};

export const deleteSurvey = (surveyId: string) => async (dispatch: AppDispatch): Promise<void> => {
  await G17Client.delete(`/surveys/${surveyId}`);
  dispatch(updateSurveyListSummaryAfterDeletion(surveyId));
  dispatch(reloadSurveyListSummary());
  dispatch(reloadSurveyList());
};

export const duplicateSurvey = async (surveyId: string, form: ConfigFormData): Promise<Pick<SurveyActionData, '_id' | 'initiativeId'>> => {
  return G17Client.post(`/surveys/${surveyId}/clone`, form)
    .then(response => response.data.data)
};

export const loadSurvey = (surveyId: string, blockingLoad = true, forceReload = false): Thunk => (dispatch, getState) => {

  if (blockingLoad) {
    // TODO: Need to move this check outside the if condition above
    // if trying to load the same survey should block unless force reload
    if (!forceReload && getState().survey.surveyId === surveyId) {
      return;
    }

    dispatch({ type: actionTypes.LOADING_SURVEY_ACTIONS, surveyId });
  }

  const options = handleTokenAuth(getState);
  const handlePromise = handleSurveyAction(dispatch);
  const axiosPromise = G17Client.get(`surveys/${surveyId}`, options);
  return handlePromise(axiosPromise)
};

export const reloadSurvey = (surveyId: string) => loadSurvey(surveyId, false);

export const loadSurveyListSummaryByInitiativeId = (initiativeId: string | undefined, blockingLoad = true): Thunk => (dispatch, getState) => {
  if (!initiativeId) {
    return dispatch(addSiteAlert({
      content: `Unable to load ${SURVEY.SINGULAR} list because there is no initiative set`,
      color: SiteAlertColors.Danger
    }));
  }

  const state = getState();
  const surveyListSummaryState = state.surveyListSummary;
  if (surveyListSummaryState.initiativeId === initiativeId && blockingLoad) {
    // No change, and blockingLoad is disabled for reload, so this is not a reload
    return;
  }

  if (blockingLoad) {
    dispatch({
      type: actionTypes.LOADING_SURVEY_LIST_SUMMARY,
      initiativeId,
      orgId: undefined
    });
  }

  const options = handleTokenAuth(getState);
  return G17Client.get(`initiatives/${initiativeId}/surveys`, options)
    .then(response => {
      dispatch({
        type: actionTypes.LOADED_SURVEY_LIST_SUMMARY,
        data: response.data.data,
        initiativeId,
        orgId: undefined
      });
    })
    .catch(handleError(dispatch, actionTypes.ERROR_SURVEY_LIST_SUMMARY));
}

export const loadSurveyListSummaryByOrgId = (orgId: string | undefined, blockingLoad = true): Thunk => (dispatch, getState) => {
  if (!orgId) {
    return dispatch(addSiteAlert({
      content: `Unable to load ${SURVEY.SINGULAR} list because there is no organiztion set`,
      color: SiteAlertColors.Danger
    }));
  }

  const state = getState();
  const surveyListSummaryState = state.surveyListSummary;
  if (surveyListSummaryState.orgId === orgId && blockingLoad) {
    // No change, and blockingLoad is disabled for reload, so this is not a reload
    return;
  }

  if (blockingLoad) {
    dispatch({
      type: actionTypes.LOADING_SURVEY_LIST_SUMMARY,
      initiativeId: undefined,
      orgId
    });
  }

  const options = handleTokenAuth(getState);
  return G17Client.get(`surveys/user/actions/org/${orgId}`, options)
    .then(response => {
      dispatch({
        type: actionTypes.LOADED_SURVEY_LIST_SUMMARY,
        data: response.data.data,
        initiativeId: undefined,
        orgId
      });
    })
    .catch(handleError(dispatch, actionTypes.ERROR_SURVEY_LIST_SUMMARY));
}

export const reloadSurveyListSummary = (): Thunk => (dispatch, getState) => {
  const surveyListSummaryState = getState().surveyListSummary;
  if (surveyListSummaryState.initiativeId) {
    return dispatch(loadSurveyListSummaryByInitiativeId(surveyListSummaryState.initiativeId, false));
  } else if(surveyListSummaryState.orgId) {
    return dispatch(loadSurveyListSummaryByOrgId(surveyListSummaryState.orgId, false));
  }
}

export const unloadSurveyListSummary = () => (dispatch: AppDispatch) => {
  return dispatch({ type: actionTypes.UNLOADING_SURVEY_LIST_SUMMARY });
}

export const updateSurveyListSummaryAfterDeletion = (surveyId: string): Thunk => (dispatch, getState) => {
  const surveyListSummaryState = getState().surveyListSummary;
  const filteredSurveyList = surveyListSummaryState.data.filter((survey) => survey._id !== surveyId);

  return dispatch({
    type: actionTypes.UPDATE_SURVEY_LIST_SUMMARY,
    data: filteredSurveyList,
  });
}

export const loadSurveyCalculationView = async (surveyId: string) => {
  return G17Client.get(`/admin/surveys/${surveyId}/view?exclude=groups,fragments`)
    .then(response => response.data.data)
}
