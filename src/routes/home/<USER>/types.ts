/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { ReferralInfoType } from '../../../types/onboarding';

export enum PartnerSectionCodes {
  Consultancy = 'consultancy',
  Fundraising = 'fundraising',
  TechnologyPartners = 'technology-partners',
  StandardBoards = 'standard-boards',
  MemberInstitutions = 'member-institutions',
}

export interface Card {
  logo: string;
  title: JSX.Element;
  subtitle?: JSX.Element;
  description?: JSX.Element;
  url?: string;
  clickHandler?: keyof ClickHandlers;
  solutions?: boolean;
  partners?: PartnerSectionCodes;
  btnOutline?: boolean;
  slug?: string;
  learnMoreLink?: string;
  tags: string[];
  actionTextSecondary?: string
  isProduct?: boolean;
  isStaffOnly?: boolean;
  sponsoredByLogo?: string;
  button?: {
    text: string;
    onClickHandler?: keyof ClickHandlers;
    disabled?: boolean;
  }
}

type handlerFunc = (card?: Card, referralInfo?: ReferralInfoType) => void;
export interface ClickHandlers {
  GoToCompanyTrackerPro: handlerFunc;
  GoToCompanyTrackerLight: handlerFunc;
  GoToCompanyTrackerRegistration: (options?: { onboardingPath?: string, referralInfo?: ReferralInfoType }) => void;
  GoToCompanyTrackerRegistrationDirect: handlerFunc;
  GoToCompanyTrackerLightInfo: handlerFunc;
  GoToESGenomeInfo: handlerFunc;
  GoToPortfolioTracker: handlerFunc;
  GoToAssurance: handlerFunc;
  GoToSDGAP: handlerFunc;
  GoToWorldTracker: handlerFunc;
  GoToEmissionsCalculator: handlerFunc;
  GoToMaterialityTracker: handlerFunc;
  ShowRequestADemo: handlerFunc;
}

export interface HomepageConfig {
  partner?: HomepagePartner;
  productCards: (props: HomepageProductCardProps) => HomepageProductCard[];
  comingSoonProductCards: HomepageProductCard[];
  featureCards?: HomepageFeatureCardGroup[];
  anchorCards?: HomepageAnchorCard[];
  featureBarCards?: HomepageFeatureBar;
}

interface HomepageProductCardButton {
  text: string;
  onClick: React.MouseEventHandler<HTMLElement>;
}

export interface HomepageProductCardProps {
  view: HomepageView;
  isUserStaff: boolean;
  isLoggedIn: boolean;
  clickHandlers: ClickHandlers;
}

export interface HomepageProductCard {
  logo: string;
  color?: string;
  title?: string;
  disabled?: boolean;
  buttons?: (HomepageProductCardButton | undefined)[];
  onClick?: React.MouseEventHandler<HTMLElement>;
  status?: string;
}

export enum HomepageView {
  UAE = 'uae',
  KSA = 'ksa',
  Singapore = 'sg',
  Europe = 'eu',
}

export type HomepagePartner = undefined | {
  logo: string;
};

export interface HomepageAnchorCard {
  image: string;
  title: string;
  anchor: string;
}

export interface HomepageViewConfig {
  title: string;
  view: HomepageView;
  flag: string;
  url: string;
  locationMatch?: {
    country?: string[];
  };
}

export interface BulletListItem {
  text: string;
}

export interface HomepageFeatureCard {
  image: string;
  title: string;
  description: string;
  imageHeight?: number;
}

export interface HomepageFeatureCardGroup {
  from: {
    logo: string;
  },
  cards: HomepageFeatureCard[];
}

interface HomepageFeatureBarCategory {
  icon: string;
  title: string;
}

interface HomepageFeatureBarCard {
  image: string;
  title: string;
  list: BulletListItem[];
}

export interface HomepageFeatureBar {
  categoryOne: HomepageFeatureBarCategory;
  categoryTwo: HomepageFeatureBarCategory;
  categoryThree: HomepageFeatureBarCategory;
  cardLeft: HomepageFeatureBarCard;
  cardRight: HomepageFeatureBarCard;
}
