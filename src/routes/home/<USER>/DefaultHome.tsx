/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { useState } from 'react';
import { Button, Modal, ModalBody, ModalHeader } from 'reactstrap';
import { Footer } from '@g17eco/molecules/footer';
import RequestDemoModal from '../../../components/request-demo-modal';
import { Header } from './partials/Header';
import { ProductCards } from './partials/ProductCards';
import { useHomepageProductCards } from './hooks/useHomepageProductCards';
import { AnchorCards } from './partials/AnchorCards';
import { useHomepageComingSoonProductCards } from './hooks/useHomepageComingSoonProductCards';
import G17EcoLogo from '../../../images/g17Eco.svg';
import { AnimatedBackground } from './partials/AnimatedBackground';
import { InPartnership } from './partials/InPartnership';
import { useViewRouterConfig } from './hooks/useViewRouterConfig';
import { useAppClickHandlers } from '../../../hooks/useAppClickHandlers';
import DisambiguationModal, { DisambiguationModalProps } from '../partials/DisambiguationModal';
import { ClickHandlers, HomepageView, HomepageViewConfig } from './types';
import { SingaporePartial } from './SingaporePartial';
import { UAEPartial } from './UAEPartial';
import { LondonPartial } from './LondonPartial';
import { useConfig } from './hooks/useConfig';
import './DefaultHome.scss';
import { DashboardDivider } from '@g17eco/atoms/divider';
import { BasicAlert } from '@g17eco/molecules/alert';

interface ModalState {
  title?: JSX.Element;
  isPartner?: boolean;
  isOpen: boolean;
}

export function DefaultHome() {
  const [submitMessage, setSubmitMessage] = useState('');
  const [showComingSoon, setComingSoon] = useState(false);
  const [requestModalOpen, setRequestDemoModalOpen] = useState<ModalState>({
    isOpen: false,
    title: <></>,
    isPartner: false,
  });
  const [disambiguationModal, setDisambiguationModal] = useState<DisambiguationModalProps | undefined>(undefined);

  const toggleComingSoon = () => setComingSoon(!showComingSoon);
  const toggleRequestDemoModal = (props?: Partial<ModalState>) => {
    setRequestDemoModalOpen({
      title: props?.title ?? <></>,
      isPartner: Boolean(props?.isPartner),
      isOpen: !requestModalOpen.isOpen,
    });
  };

  const { viewConfig, handleChangeViewConfig } = useViewRouterConfig();
  const clickHandlers = useAppClickHandlers({ toggleRequestDemoModal, setDisambiguationModal });
  const productCards = useHomepageProductCards({ view: viewConfig.view, clickHandlers });
  const comingSoonProductCards = useHomepageComingSoonProductCards({ view: viewConfig.view });
  const { anchorCards, partner } = useConfig({ view: viewConfig.view });

  const customTitle = requestModalOpen.title?.props.children;
  const title = customTitle ? `Get in touch with ${customTitle}` : undefined;

  return (
    <AnimatedBackground className='homepage-container d-flex flex-column p-4'>
      <BasicAlert className={'text-center success-alert-container'} type={'success'}>
        {submitMessage}
      </BasicAlert>

      <div className='homepage-inner-container mt-4'>
        <Header viewConfig={viewConfig} onChange={handleChangeViewConfig} />

        <InPartnership partner={partner} />

        {anchorCards && anchorCards.length > 0 ? (
          <div className='mt-6'>
            <AnchorCards cards={anchorCards} />
          </div>
        ) : null}

        <h2 id='reporting-tools' className='mt-6'>
          Digital Reporting Tools
        </h2>

        <div className='mt-4'>
          <ProductCards cards={productCards} showContactUs={true} clickHandlers={clickHandlers} />
        </div>

        <h2 id='coming-soon' className='mt-6'>
          Coming Soon
        </h2>

        <div className='mt-4'>
          <ProductCards cards={comingSoonProductCards} clickHandlers={clickHandlers} />
        </div>

        <DashboardDivider className='mt-6 w-25 mx-auto' />

        <View viewConfig={viewConfig} clickHandlers={clickHandlers} />
      </div>

      <Modal isOpen={showComingSoon} toggle={toggleComingSoon} backdrop='static'>
        <ModalHeader toggle={toggleComingSoon}>Coming Soon</ModalHeader>
        <ModalBody>
          This feature is not ready yet. Please check back later.
          <div className='mt-3 text-right'>
            <Button onClick={() => setComingSoon(false)}>OK</Button>
          </div>
        </ModalBody>
      </Modal>

      <RequestDemoModal
        isOpen={requestModalOpen.isOpen}
        toggle={toggleRequestDemoModal}
        onSubmit={() => {
          setSubmitMessage('Message sent. We’ll be in touch shortly.');
          setTimeout(() => setSubmitMessage(''), 10000);
        }}
        interestedIn={requestModalOpen.isPartner ? 'Partners' : ''}
        title={title}
      />

      <div className='text-center'>
        <img src={G17EcoLogo} width={110} alt='G17Eco Logo' className='mt-6' />
      </div>
      {disambiguationModal ? <DisambiguationModal {...disambiguationModal} /> : null}

      <Footer />
    </AnimatedBackground>
  );
}

interface ViewProps {
  viewConfig: HomepageViewConfig;
  clickHandlers: ClickHandlers;
}

const View = (props: ViewProps) => {
  const { viewConfig, clickHandlers } = props;

  switch (viewConfig.view) {
    case HomepageView.Singapore:
      return <SingaporePartial clickHandlers={clickHandlers} />;
    case HomepageView.KSA:
    case HomepageView.UAE:
      return <UAEPartial clickHandlers={clickHandlers} />;
    case HomepageView.Europe:
    default:
      return <LondonPartial clickHandlers={clickHandlers} />;
  }
};
