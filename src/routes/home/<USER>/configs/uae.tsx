/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { AppFlavour, getBrandingConfig } from '../../../../config/app-config';
import { HomepageProductCard, HomepageProductCardProps, HomepageConfig } from '../types';
import { createOnClickFromExternalUrl, createOnClickFromHandler } from '../utils';
import {
  assuranceTrackerCard,
  cityTrackerCard,
  emissionsTrackerCard,
  materialityTrackerCard,
  nationTrackerCard,
  portfolioTrackerCard,
  projectTrackerCard,
  sDGapCard,
  worldTrackerCard
} from './_common';
import config from '../../../../config';
import { CyberQFeatureImg, CyberQLogo, FusangLogo, KyndrylFeatureImg, KyndrylLogo } from './imageConfig';

const FeatureImg1 = `${config.media.imagesBaseUrl}/home/<USER>
const FeatureImg2 = `${config.media.imagesBaseUrl}/home/<USER>
const FeatureImg3 = `${config.media.imagesBaseUrl}/home/<USER>
const FeatureImg6 = `${config.media.imagesBaseUrl}/home/<USER>
const FeatureImg7 = `${config.media.imagesBaseUrl}/home/<USER>
const MecalcLogo = `${config.media.imagesBaseUrl}/home/<USER>
const EAndEnterpriseLogo = `${config.media.imagesBaseUrl}/home/<USER>
const HaifinLogo = `${config.media.imagesBaseUrl}/home/<USER>
const AnchorImg1 = `${config.media.imagesBaseUrl}/home/<USER>
const AnchorImg2 = `${config.media.imagesBaseUrl}/home/<USER>
const AnchorImg3 = `${config.media.imagesBaseUrl}/home/<USER>
const AnchorImg4 = `${config.media.imagesBaseUrl}/home/<USER>
const AnchorImg5 = `${config.media.imagesBaseUrl}/home/<USER>
const BeehiveLogo = `${config.media.imagesBaseUrl}/home/<USER>

const ctlApp = getBrandingConfig(AppFlavour.DEFAULT).branding.ctlApp;

export const homepageConfig: HomepageConfig = {
  partner: {
    logo: EAndEnterpriseLogo,
  },
  productCards: (props: HomepageProductCardProps): HomepageProductCard[] => [
    materialityTrackerCard(props),
    {
      title: 'Company Tracker',
      logo: ctlApp.cardLogo,
      color: '#********',
      onClick: createOnClickFromHandler('GoToCompanyTrackerLight', props.clickHandlers),
      buttons: [
        {
          text: props.isLoggedIn ? 'Access' : 'Log in',
          onClick: createOnClickFromHandler('GoToCompanyTrackerLight', props.clickHandlers),
        },
        {
          text: 'Learn More',
          onClick: createOnClickFromExternalUrl(config.brochureURL.companyTracker),
        },
      ],
    },
    assuranceTrackerCard(props),
    portfolioTrackerCard(props),
    emissionsTrackerCard(props),
    worldTrackerCard(props),
    sDGapCard(props),
  ],
  comingSoonProductCards: [projectTrackerCard, cityTrackerCard, nationTrackerCard],
  featureCards: [
    {
      from: {
        logo: EAndEnterpriseLogo,
      },
      cards: [
        {
          image: FeatureImg1,
          title: 'IoT and AI',
          description: `Covering solar PV, MEP retrofits, smart building, lighting, smart home,
    fleet management, connected worker, smart office, smart parking, environmental sensing,
    water quality, waste management, and electric car charging.`,
        },
        {
          image: FeatureImg2,
          title: 'Data mapping, APIs, Systems Intergations, SSO, ESG Data Lakes',
          description: `Our IT solutions maximise efficiency & sustainability, making digital
    infrastructures robust & eco-conscious. We ensure that digital frameworks are not only
    robust & reliable but also eco-conscious.`,
        },
        {
          image: FeatureImg3,
          title: 'Digital Transformation, automation, risk management of operations and supply chains',
          description: `Our comprehensive approach to business continuity encompasses advanced planning,
        robust infrastructure, & adaptive solutions.
        `,
        },
      ],
    },
    {
      from: {
        logo: KyndrylLogo,
      },
      cards: [
        {
          image: KyndrylFeatureImg,
          title: 'Tech & Sustainability Consulting',
          description: 'Including Microsoft Sustainability Manager; Device Enterprise Management on ESG factors; Kyndryl Mindful employee well-being consulting; Employee Dashboard for elevated employee engagement programs.',
        },
      ],
    },
    {
      from: {
        logo: CyberQLogo,
      },
      cards: [
        {
          image: CyberQFeatureImg,
          title: 'Cyber Security Governance',
          description: `A cyber framework that makes it easy to introduce cyber expertise and services
        into your organisation as you need them. Every business is on a different journey so you need
        a cyber partner that protects you now and can scale.`,
        },
      ],
    },
    {
      from: {
        logo: MecalcLogo,
      },
      cards: [
        {
          image: FeatureImg6,
          title: 'Breakthrough sensor monitoring',
          description: 'Breakthrough frequency sensor monitoring technologies to prevent noise pollution, and support product, equipment and environmental compliance or early warning degradation.',
        },
      ],
    },
    {
      from: {
        logo: HaifinLogo,
      },
      cards: [
        {
          image: FeatureImg7,
          title: 'Trade Finance Fraud Prevention for Banks and Fintechs',
          description: `AI and machine learning analyze trends, enabling highly informed
        decisions. They adeptly handle complex tasks, compare ambiguous data, and detect
        suspicious values, figures, and patterns.`,
        },
      ],
    },
  ],
  anchorCards: [
    {
      image: AnchorImg1,
      title: 'Reporting Tools',
      anchor: 'reporting-tools',
    },
    {
      image: AnchorImg2,
      title: 'Education and Training',
      anchor: 'education-and-training',
    },
    {
      image: AnchorImg3,
      title: 'Consultancy',
      anchor: 'consultancy',
    },
    {
      image: AnchorImg4,
      title: 'Sustainability Solutions',
      anchor: 'sustainable-solutions',
    },
    {
      image: AnchorImg5,
      title: 'Sustainable Finance',
      anchor: 'sustainable-finance',
    },
  ],
  featureBarCards: {
    categoryOne: {
      icon: 'fa-money-check-dollar-pen',
      title: 'Liquidity Events',
    },
    categoryTwo: {
      icon: 'fa-coins',
      title: 'Microfinance',
    },
    categoryThree: {
      icon: 'fa-money-bill-transfer',
      title: 'Sustainability Rebates',
    },
    cardLeft: {
      image: FusangLogo,
      title: `Connects private markets and digital assets. Fusang's
    Exchange provides seamless access to liquidity at scale`,
      list: [
        { text: 'Sukuks' },
        { text: 'Equities' },
        { text: 'Access liquidity globally' },
        { text: 'Security Tokenisation Social, Economic and Environmental Impact Bonds' },
      ],
    },
    cardRight: {
      image: BeehiveLogo,
      title: 'A peer to peer lending platform working to address the $250bn SME credit gap across the GCC',
      list: [
        { text: 'Microfinance' },
        { text: 'Working Capital' },
        { text: 'Term Finance' },
        { text: 'Peer to Peer Lending for SMEs with Sustainable Finance Rebates' },
      ],
    },
  },
};
