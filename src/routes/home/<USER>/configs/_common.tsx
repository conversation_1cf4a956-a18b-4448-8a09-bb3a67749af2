/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import PTLogo from '../../../../images/apps/Portfolio_Tracker_logo.svg';
import SDGAPLogo from '../../../../images/apps/SDGAP_logo.svg';
import WTLogo from '../../../../images/apps/World_Tracker_logo.svg';
import MTLogo from '../../../../images/apps/Materiality_Tracker_logo.svg';
import EmissionsLogo from '../../../../images/apps/Emissions_Tracker_logo.svg';
import AssuranceTrackerLogo from '../../../../images/apps/Assurance_Tracker_logo.svg';
import ProjectTrackerLogo from '../../../../images/apps/Project_Tracker_logo.svg';
import CityTrackerLogo from '../../../../images/apps/City_Tracker_logo.svg';
import NationTrackerLogo from '../../../../images/apps/Nation_Tracker_logo.svg';
import { HomepageProductCard, HomepageProductCardProps } from '../types';
import { createOnClickFromExternalUrl, createOnClickFromHandler } from '../utils';
import { AppFlavour, getBrandingConfig } from '../../../../config/app-config';
import config from '../../../../config';

export const materialityTrackerCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'Materiality Tracker',
  logo: MTLogo,
  color: '#69157211',
  onClick: props.isLoggedIn
    ? createOnClickFromHandler('GoToMaterialityTracker', props.clickHandlers)
    : createOnClickFromExternalUrl(config.brochureURL.materialityTracker),
  buttons: [
    {
      text: props.isLoggedIn ? 'Access' : 'Log in',
      onClick: createOnClickFromHandler('GoToMaterialityTracker', props.clickHandlers)
    },
    {
      text: 'Learn More',
      onClick: createOnClickFromExternalUrl(config.brochureURL.materialityTracker),
    },
  ],
});

const ctlApp = getBrandingConfig(AppFlavour.DEFAULT).branding.ctlApp;
export const companyTrackerCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'Company Tracker',
  logo: ctlApp.cardLogo,
  color: '#********',
  onClick: createOnClickFromHandler('GoToCompanyTrackerLight', props.clickHandlers),
  buttons: [
    {
      text: props.isLoggedIn ? 'Access' : 'Log in',
      onClick: createOnClickFromHandler('GoToCompanyTrackerLight', props.clickHandlers),
    },
    {
      text: 'Learn More',
      onClick: createOnClickFromExternalUrl(config.brochureURL.companyTracker),
    },
  ],
});

const sgxApp = getBrandingConfig(AppFlavour.SINGAPORE).branding.ctlApp;
export const esgenomeCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'ESGenome',
  logo: sgxApp.cardLogo,
  color: '#3778FF11',
  onClick: createOnClickFromHandler('GoToCompanyTrackerLight', props.clickHandlers),
  buttons: [
    {
      text: props.isLoggedIn ? 'Access' : 'Log in',
      onClick: createOnClickFromHandler('GoToCompanyTrackerLight', props.clickHandlers),
    },
    !props.isLoggedIn
      ? {
          text: 'Sign up',
          onClick: createOnClickFromHandler('GoToESGenomeInfo', props.clickHandlers),
        }
      : undefined,
    {
      text: 'Learn More',
      onClick: createOnClickFromHandler('GoToESGenomeInfo', props.clickHandlers),
    },
  ],
});

export const assuranceTrackerCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'Assurance Tracker',
  logo: AssuranceTrackerLogo,
  color: '#FBD02E11',
  onClick: createOnClickFromHandler('GoToAssurance', props.clickHandlers),
  buttons: [
    {
      text: props.isLoggedIn ? 'Access' : 'Log in',
      onClick: createOnClickFromHandler('GoToAssurance', props.clickHandlers),
    },
    {
      text: 'Learn More',
      onClick: createOnClickFromExternalUrl(config.brochureURL.assuranceTracker),
    },
  ],
});

export const portfolioTrackerCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'Portfolio Tracker',
  logo: PTLogo,
  color: '#0FAA1711',
  onClick: createOnClickFromHandler('GoToPortfolioTracker', props.clickHandlers),
  buttons: [
    {
      text: props.isLoggedIn ? 'Access' : 'Log in',
      onClick: createOnClickFromHandler('GoToPortfolioTracker', props.clickHandlers),
    },
    {
      text: 'Learn More',
      onClick: createOnClickFromExternalUrl(config.brochureURL.portfolioTracker),
    },
  ],
});

export const emissionsTrackerCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'Emissions Tracker',
  logo: EmissionsLogo,
  color: '#3778FF11',
  onClick: createOnClickFromHandler('GoToEmissionsCalculator', props.clickHandlers),
  buttons: [
    {
      text: 'Learn More',
      onClick: createOnClickFromExternalUrl(config.brochureURL.emissionsTracker),
    },
  ],
});

export const sDGapCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'SD(G)AP',
  logo: SDGAPLogo,
  color: 'linear-gradient(to right, #3778FF22, #FBD02E11, #0FAA1711, #EF382422)',
  onClick: createOnClickFromHandler('GoToSDGAP', props.clickHandlers),
  buttons: [
    {
      text: 'View',
      onClick: createOnClickFromHandler('GoToSDGAP', props.clickHandlers),
    },
  ],
});

export const worldTrackerCard = (props: HomepageProductCardProps): HomepageProductCard => ({
  title: 'World Tracker',
  logo: WTLogo,
  color: 'linear-gradient(to right, #3778FF22, #FBD02E11, #0FAA1711, #EF382422)',
  onClick: createOnClickFromHandler('GoToWorldTracker', props.clickHandlers),
  buttons: [
    {
      text: 'View',
      onClick: createOnClickFromHandler('GoToWorldTracker', props.clickHandlers),
    },
  ],
});

export const projectTrackerCard: HomepageProductCard = {
  title: 'Project Tracker',
  logo: ProjectTrackerLogo,
  color: '#3F1FF311',
};

export const cityTrackerCard: HomepageProductCard = {
  title: 'City Tracker',
  logo: CityTrackerLogo,
  color: '#0F625611',
};

export const nationTrackerCard: HomepageProductCard = {
  title: 'Nation Tracker',
  logo: NationTrackerLogo,
  color: '#CE66A411',
};
