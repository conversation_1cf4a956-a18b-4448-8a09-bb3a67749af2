/*
 * Copyright (c) 2021. World Wide Generation Ltd
 */

import {
  AppIds,
  CompanyTrackerIds,
  CompanyTrackerLightIds,
  PortfolioTrackerIds
} from '@utils/permission-groups';
import { generateUrl } from './util';
import { ROUTES } from '@constants/routes';
import { InitiativeType, RootInitiativeData } from '../types/initiative';
import { SettingStorage } from '@services/SettingStorage';
import { companyTrackerAppCodes } from '@utils/apps';
import { ProductCodes } from '../services/SubscriptionService';

export type AppRouteMatcherInitiative = Pick<
  RootInitiativeData,
  '_id' | 'name' | 'firstInitiativeId' | 'tags' | 'appConfig' | 'permissionGroup' | 'type'
> &
  Partial<Pick<RootInitiativeData, 'calculatedSubscriptions'>>;


export interface AppRootMatcher {
  storageKey: string;
  filterInitiatives: <T extends AppRouteMatcherInitiative>(initiatives: T[]) => T[];
  generateUrl: (id: string, rootAppPath?: string) => string;
}

export const companyTrackerRouteMatcher: AppRootMatcher = {
  storageKey: AppIds.CompanyTracker,
  filterInitiatives: (initiatives) => initiatives.filter((r) => {
    if (r.appConfig && !companyTrackerAppCodes.includes(r.appConfig.code)) {
      // If there is an appConfig, check if it is a CompanyTracker app, otherwise ignore
      return false;
    }

    return [
      ...CompanyTrackerIds,
      ...CompanyTrackerLightIds
    ].includes(r.permissionGroup as string);
  }),
  generateUrl: (id: string, rootAppPath) => generateUrl(ROUTES.COMPANY_TRACKER_SURVEY_REDIRECTOR, {
    rootAppPath: rootAppPath || 'company-tracker',
    initiativeId: id
  }),
}

export const materialityTrackerRouteMatcher: AppRootMatcher = {
  storageKey: AppIds.MaterialityTracker,
  filterInitiatives: (initiatives) =>
    initiatives.filter((r) => {
      return r.calculatedSubscriptions?.some((s) => s.items.some((i) => i.productCode === ProductCodes.MaterialityTracker));
    }),
  generateUrl: (id: string) => generateUrl(ROUTES.MATERIALITY_TRACKER_REDIRECT, {
    initiativeId: id
  }),
}

export const portfolioTrackerRouteMatcher: AppRootMatcher = {
  storageKey: AppIds.PortfolioTracker,
  filterInitiatives: (initiatives) => initiatives.filter((r) => {
    return PortfolioTrackerIds.includes(r.permissionGroup as string) && r.type === InitiativeType.initiativeGroup;
  }),
  generateUrl: (id: string) => generateUrl(id ? ROUTES.PORTFOLIO_TRACKER_PORTFOLIO : ROUTES.PORTFOLIO_TRACKER, { portfolioId: id }),
}

export const assuranceRouteMatcher: AppRootMatcher = {
  storageKey: AppIds.Assurance,
  filterInitiatives: () => [],
  generateUrl: () => generateUrl(ROUTES.ASSURANCE),
}

const supportedApps = {
  [AppIds.CompanyTracker]: companyTrackerRouteMatcher,
  [AppIds.CompanyTrackerLight]: companyTrackerRouteMatcher,
  [AppIds.PortfolioTracker]: portfolioTrackerRouteMatcher,
  [AppIds.Assurance]: assuranceRouteMatcher,
  [AppIds.MaterialityTracker]: materialityTrackerRouteMatcher,
}

export const navigateToRootApp = (initiatives: RootInitiativeData[], appId: keyof typeof supportedApps) => {
  const matcher = supportedApps[appId];
  const roots = matcher.filterInitiatives(initiatives);

  const selectedId = SettingStorage.getItem(matcher.storageKey);
  const rootInitiative = roots.find((o) => o._id === selectedId) ?? roots[0];

  // Always prefer firstInitiativeId if available,
  // as user might not have access to rootInitiative directly
  return matcher.generateUrl(rootInitiative?.firstInitiativeId ?? rootInitiative?._id ?? '');
}
