/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, fireEvent } from '@testing-library/react';
import { UserTable, UserTableUser } from './UserTable';
import { UserRoles } from '@constants/user';
import { ExtendedRenderOptions, renderWithProviders } from '@fixtures/utils';
import { configureStore } from '@reduxjs/toolkit';
import { reducer } from '@reducers/index';
import { faker } from '@faker-js/faker';
import { getCurrentUserState, userOne } from '@fixtures/user-factory';
import { setupServer } from 'msw/node';

const mockUsers: UserTableUser[] = [
  {
    _id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    roles: [UserRoles.Manager],
    lastUpdated: '2023-01-01',
    lastLogin: '2023-01-02',
    isActivated: true,
    isRequestToJoin: false,
    isInherited: false,
    isStaff: false,
  },
  {
    _id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    roles: [UserRoles.User],
    lastUpdated: '2023-01-01',
    lastLogin: '2023-01-02',
    isActivated: false,
    isRequestToJoin: true,
    isInherited: false,
    isStaff: false,
  },
];

const mockHandleReload = vi.fn();
const mockToggleUserInvitationForm = vi.fn();
const mockHandleReportingLevelChange = vi.fn();

describe('UserTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const server = setupServer(...[]);

  beforeAll(() => server.listen());
  afterEach(() => {
    server.resetHandlers();
    vi.clearAllMocks();
  })
  afterAll(() => server.close());

  const _id = faker.database.mongodbObjectId();
  const route: ExtendedRenderOptions['route'] = {
    initialEntries: [`/company-tracker/admin/${_id}/manage-users`],
    path: '/company-tracker/admin/:initiativeId/manage-users',
  }

  const currentUserState = getCurrentUserState(userOne);
  const renderOptions = {
    route,
    store: configureStore({
      reducer,
      preloadedState: {
        currentUser: currentUserState,
      },
    }),
  };

  it('renders the UserTable component', () => {

    renderWithProviders(
      <UserTable
        hasOwner={false}
        handleReload={mockHandleReload}
        users={mockUsers}
        toggleUserInvitationForm={mockToggleUserInvitationForm}
        handleReportingLevelChange={mockHandleReportingLevelChange}
      />,
      renderOptions
    );

    expect(screen.getByPlaceholderText('Search for user...')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('filters users based on search input', () => {
    renderWithProviders(
      <UserTable
        hasOwner={false}
        handleReload={mockHandleReload}
        users={mockUsers}
        toggleUserInvitationForm={mockToggleUserInvitationForm}
        handleReportingLevelChange={mockHandleReportingLevelChange}
      />,
      renderOptions
    );

    fireEvent.change(screen.getByPlaceholderText('Search for user...'), { target: { value: 'Jane' } });
    expect(screen.queryByText('John Doe')).not.toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('calls toggleUserInvitationForm when Add user button is clicked', () => {
    renderWithProviders(
      <UserTable
        hasOwner={false}
        handleReload={mockHandleReload}
        users={mockUsers}
        toggleUserInvitationForm={mockToggleUserInvitationForm}
        handleReportingLevelChange={mockHandleReportingLevelChange}
      />,
      renderOptions
    );

    fireEvent.click(screen.getByTestId('add-user-btn'));
    expect(mockToggleUserInvitationForm).toHaveBeenCalled();
  });
});
