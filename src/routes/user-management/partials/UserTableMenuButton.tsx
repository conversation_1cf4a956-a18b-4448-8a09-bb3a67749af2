/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useState } from 'react';
import {
  DropdownItem,
  DropdownMenu,
  DropdownToggle,
  UncontrolledDropdown,
} from 'reactstrap';
import { UserMessageModal } from '../../../components/message-modal/MessageModal';
import { UserTableUser } from '../UserTable';
import { UserRoles } from '../../../constants/user';
import { useAppDispatch, useAppSelector } from '../../../reducers';
import { isOrgManager, isOrgOwner } from '../../../selectors/user';
import { updateOwner } from '../../../actions/initiative';
import { AnalyticsEvents } from '../../../services/analytics/AnalyticsEvents';
import { getAnalytics } from '../../../services/analytics/AnalyticsService';
import { SiteAlertColors } from '../../../slice/siteAlertsSlice';
import { BlockingLoader } from '@g17eco/atoms/loader';
import { loadCurrentUser } from '../../../actions/user';
import { getRootOrg } from '../../../selectors/initiative';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import {
  useRemoveOnboardingMutation,
  useRemovePermissionFromOnboardingMutation,
  useRemoveUserMutation,
} from '@api/manage-users';
import { useSiteAlert } from '@hooks/useSiteAlert';

interface UserTableMenuButtonProps {
  user: UserTableUser;
  initiativeId: string;
  handleReload: () => Promise<void>;
  hasOwner: boolean;
}

const isOnboardingOwnByCurrentLevel = (user: Pick<UserTableUser, 'invitation'>, initiativeId: string) => {
  return user.invitation && user.invitation.initiativeId === initiativeId;
};

export const UserTableMenuButton = (props: UserTableMenuButtonProps) => {
  const { user, initiativeId, handleReload, hasOwner } = props;

  const [messageUserId, setMessageUser] = useState<string | undefined>();
  const [isSaving, setSaving] = useState(false);

  const dispatch = useAppDispatch();
  const { addSiteAlert, addSiteError } = useSiteAlert();

  const rootOrg = useAppSelector(getRootOrg);
  const isCurrentUserOwner = useAppSelector(isOrgOwner);
  const isCurrentUserManager = useAppSelector(isOrgManager);
  const isUserOwner = user.roles.includes(UserRoles.Owner);

  const isRootOrg = rootOrg?._id === initiativeId;
  const anyManagerCanSetOwner = !hasOwner && isCurrentUserManager;
  const canSetOwner = isRootOrg && (anyManagerCanSetOwner || (isCurrentUserOwner && !isUserOwner));

  const [removeUser, { isLoading: isRemovingUser }] = useRemoveUserMutation();
  const [removeOnboarding, { isLoading: isRemovingOnboarding }] = useRemoveOnboardingMutation();
  const [removePermissionFromOnboarding, { isLoading: isRemovingPermissionFromOnboarding }] =
    useRemovePermissionFromOnboardingMutation();

  const getRemoveHandler = (user: UserTableUser) => {
    if (user.isActivated) {
      return removeUser;
    }
    return isOnboardingOwnByCurrentLevel(user, initiativeId) ? removeOnboarding : removePermissionFromOnboarding;
  };

  const handleRemoveUser = async (user: UserTableUser) => {
    const removeHandler = getRemoveHandler(user);

    removeHandler({ initiativeId, userId: user._id })
      .unwrap()
      .then(async () => {
        if (isOnboardingOwnByCurrentLevel(user, initiativeId)) {
          const analytics = getAnalytics();
          await analytics.track(AnalyticsEvents.UserInvitationRevoked, {
            initiativeId,
            onboardId: user._id,
          });
        }
        await handleReload();
        addSiteAlert({
          content: `User ${user.name} removed.`,
          color: SiteAlertColors.Info,
          timeout: 5000,
        });
      })
      .catch((e) => {
        addSiteError(e);
      });
  };

  const handleSetOwner = async (user: UserTableUser) => {
    if (!canSetOwner) {
      return;
    }
    setSaving(true);
    try {
      await updateOwner(initiativeId, user._id);
      dispatch(loadCurrentUser(true));
      await handleReload();
      addSiteAlert({
        content: `Ownership transferred from you to ${user.name}.`,
        color: SiteAlertColors.Info,
        timeout: 5000,
      });
      setSaving(false);
    } catch (e) {
      addSiteError(e);
      setSaving(false);
    }
  };

  if (!user.isActivated) {
    return (
      <UncontrolledDropdown>
        <DropdownToggle color='transparent'>
          <i className='fas fa-ellipsis text-ThemeAccentMedium' />
        </DropdownToggle>
        <DropdownMenu>
          <DropdownItem size='sm' onClick={() => handleRemoveUser(user)}>
            <i className='fa fa-times mr-2 text-ThemeDangerMedium' />
            Revoke invitation
          </DropdownItem>
        </DropdownMenu>
      </UncontrolledDropdown>
    );
  }

  const isOwner = user.roles.includes(UserRoles.Owner);

  const isRemoving = isRemovingPermissionFromOnboarding || isRemovingOnboarding || isRemovingUser;

  return (
    <>
      {isSaving || isRemoving ? <BlockingLoader /> : null}
      {messageUserId ? <UserMessageModal toggle={() => setMessageUser(undefined)} userIds={[messageUserId]} /> : null}
      <UncontrolledDropdown>
        <DropdownToggle color='transparent'>
          <i className='fas fa-ellipsis text-ThemeAccentMedium' />
        </DropdownToggle>
        <DropdownMenu>
          {canSetOwner ? (
            <DropdownItem size='sm' onClick={() => handleSetOwner(user)}>
              <i className='fa fa-house-user mr-2' />
              Set as owner
            </DropdownItem>
          ) : null}
          <DropdownItem size='sm' onClick={() => setMessageUser(user._id)}>
            <i className='fa fa-envelope mr-2' />
            Message user
          </DropdownItem>
          <SimpleTooltip text={isOwner ? 'Cannot remove Owner' : ''}>
            <DropdownItem size='sm' onClick={() => handleRemoveUser(user)} disabled={isOwner}>
              <i className='fa fa-times mr-2 text-ThemeDangerMedium' />
              Remove user
            </DropdownItem>
          </SimpleTooltip>
        </DropdownMenu>
      </UncontrolledDropdown>
    </>
  );
};
