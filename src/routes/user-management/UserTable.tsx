/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import React, { useEffect, useState } from 'react';
import { Button, Input } from 'reactstrap';
import { DATE, formatDate, formatDateUTC } from '../../utils/date';
import { companyTrackerUserRoles, getRoleName, portfolioTrackerUserRoles, UserFilterReportingLevel, UserRoles } from '../../constants/user';
import { useHistory, useLocation, useRouteMatch } from 'react-router-dom';
import { UserFilter } from './UserFilter';
import { useAppSelector } from '../../reducers';
import { escapeRegexCharacters } from '../../utils/string-format';
import { UserTableMenuButton } from './partials/UserTableMenuButton';
import { UserRolesDropdown } from './partials/UserRolesDropdown';
import { OnboardingStatus } from '../../types/onboarding';
import './styles.scss';
import { RequestToJoinModal } from './RequestToJoinModal';
import { SelectOption } from '../../types/select';
import IconButton from '@components/button/IconButton';
import { exportToExcel } from '@components/downloads/util/exportToExcel';
import { getInitiativesTreeMapById } from '@selectors/initiativeTree';
import { BasicAlert } from '@g17eco/molecules/alert';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { Table, ColumnDef } from '@g17eco/molecules/table';
import { ManagementInvitation, ManagementUser } from '@constants/users';
import { naturalSort } from '@utils/index';

export interface UserTableUser {
  _id: string;
  name?: string;
  email?: string;
  roles: UserRoles[];
  lastUpdated?: string;
  lastLogin?: string;
  isActivated: boolean;
  isRequestToJoin: boolean;
  isInherited: boolean;
  isStaff: boolean;
  user?: ManagementUser;
  invitation?: ManagementInvitation;
  invitedDate?: string;
}

interface UserTableProps {
  handleReload: () => Promise<void>;
  users: UserTableUser[];
  toggleUserInvitationForm: () => void;
  isAddUserDisabled?: boolean;
  btnText?: string;
  filterReportingLevel?: UserFilterReportingLevel;
  handleReportingLevelChange: (newValue: any) => void;
  isDownloadable?: boolean;
  hasOwner: boolean;
}

const getStatus = (user: UserTableUser): string => {
  if (user.isActivated) {
    return 'Active';
  }
  if (user.isRequestToJoin) {
    return 'Not Started';
  }
  return 'Pending';
}

export const UserTable = (props: UserTableProps) => {
  const {
    handleReload,
    users,
    toggleUserInvitationForm,
    isAddUserDisabled = false,
    btnText = 'Add user',
    filterReportingLevel,
    handleReportingLevelChange,
    isDownloadable = false,
    hasOwner
  } = props;

  const match = useRouteMatch<{ initiativeId?: string, portfolioId?: string }>();
  const location = useLocation();
  const history = useHistory();

  const initiativeId = match.params.initiativeId ?? match.params.portfolioId ?? '';
  const roles = match.params.portfolioId ? portfolioTrackerUserRoles : companyTrackerUserRoles;

  const [searchText, setSearchText] = useState('');
  const [filteredUsers, setFilteredUsers] = useState<(UserTableUser[])>(users);
  const [requestToJoinModal, setRequestToJoinModal] = useState<ManagementInvitation | undefined>(undefined);

  const [filterStatus, setFilterStatus] = useState<'active' | 'pending' | undefined>();
  const [filterPermission, setFilterPermission] = useState<UserRoles | undefined>();
  const currentUserState = useAppSelector((state) => state.currentUser);

  const initiativeTree = useAppSelector(getInitiativesTreeMapById);

  useEffect(() => {
    if (users.length > 0 && location.search) {
      const searchParams = new URLSearchParams(location.search);
      const id = searchParams.get('onboardingId');
      if (id) {
        const user = users.find(u => u.invitation?._id === id);
        if (user?.invitation && user.invitation.status === OnboardingStatus.NotStarted) {
          setRequestToJoinModal(user.invitation)
        }
        searchParams.delete('onboardingId');
        history.replace({ pathname: location.pathname, search: searchParams.toString() });
      }
    }
  }, [history, location.pathname, location.search, users]);

  React.useEffect(() => {
    const filterUsers = (user: UserTableUser) => {
      if (filterStatus) {
        if (filterStatus === 'active' && !user.isActivated) {
          return false;
        }
        if (filterStatus === 'pending' && user.isActivated) {
          return false;
        }
      }

      if (filterPermission) {
        if (!user.roles.includes(filterPermission)) {
          return false;
        }
      }

      if (searchText) {
        const escapedSearchText = escapeRegexCharacters(searchText.trim().toLowerCase());
        const searchTextRegex = new RegExp(escapedSearchText.replace(/ /g, '|'), 'g');
        if ((`${user.name}`.toLowerCase().match(searchTextRegex) || []).length > 0) {
          return true;
        }
        if ((`${user.email}`.toLowerCase().match(searchTextRegex) || []).length > 0) {
          return true;
        }
        return false;
      }
      return true;
    };
    setFilteredUsers(users.filter(filterUsers));
  }, [users, initiativeId, searchText, filterStatus, filterPermission]);

  const handlePermissionChange = (filter: SelectOption<UserRoles | undefined> | SelectOption<UserRoles | undefined>[]) => {
    if (Array.isArray(filter)) {
      return;
    }
    setFilterPermission(filter?.value ?? undefined)
  };

  const handleStatusChange = (filter: SelectOption<'active' | 'pending' | undefined> | SelectOption<'active' | 'pending' | undefined>[]) => {
    if (Array.isArray(filter)) {
      return;
    }
    setFilterStatus(filter?.value ?? undefined)
  };

  const isCurrentUserOrInherited = (user: UserTableUser): boolean => {
    const isCurrentUser = user._id === currentUserState.data._id;
    return user.isInherited || isCurrentUser;
  }

  const tableColumns: ColumnDef<UserTableUser>[] = [
    {
      id: '_id',
      header: '',
      meta: {
        cellProps: {
          className: 'text-center',
        },
      },
      cell: ({ row }) => {
        const user = row.original;
        // This tooltip shows for inherited users, but not for onboardings invited at this initiative level
        if (user.isInherited && user.invitation?.initiativeId !== initiativeId) {
          return (
            <SimpleTooltip text='This user has permissions that were inherited from another reporting level. You cannot manage this user from this level.'>
              <i className='fa-light fa fa-circle-info' color='secondary' />
            </SimpleTooltip>
          );
        }

        const isCurrentUser = user._id === currentUserState.data._id;
        if (isCurrentUser) {
          return (
            <SimpleTooltip text='You cannot modify your own permissions.'>
              <i className='fa-light fa fa-circle-info' color='secondary' />
            </SimpleTooltip>
          );
        }
        return (
          <UserTableMenuButton
            hasOwner={hasOwner}
            user={user}
            initiativeId={initiativeId}
            handleReload={handleReload}
          />
        );
      },
    },
    {
      accessorKey: 'name',
      header: 'Name',
      meta: {
        cellProps: {
          className: 'dont_translate',
        },
      },
      cell: ({ row }) => {
        const user = row.original;
        return <div className={`${isCurrentUserOrInherited(user) ? 'text-ThemeBgDisabled' : ''}`}>{user.name}</div>;
      },
    },
    {
      accessorKey: 'email',
      header: 'Email address',
      meta: {
        cellProps: {
          className: 'emailCol dont_translate',
        },
      },
      cell: ({ row }) => {
        const user = row.original;
        return (
          <SimpleTooltip text={user.email}>
            <div
              className={`${
                isCurrentUserOrInherited(user) ? 'text-ThemeBgDisabled' : ''
              } hover-expand text-truncate dont_translate`}
            >
              {user.email}
            </div>
          </SimpleTooltip>
        );
      },
    },
    {
      accessorKey: 'roles',
      header: 'Permissions',
      cell: ({ row }) => {
        const user = row.original;
        if (!currentUserState.loaded) {
          return;
        }

        return (
          <UserRolesDropdown
            currentUser={currentUserState.data}
            user={user}
            initiativeId={initiativeId}
            roles={roles}
            handleReload={handleReload}
          />
        );
      },
    },
    {
      accessorKey: 'lastLogin',
      header: 'Last login',
      sortingFn: (a, b) => naturalSort(a.original.lastLogin ?? '', b.original.lastLogin ?? ''),
      cell: ({ row }) => {
        const user = row.original;
        return (
          <span className={isCurrentUserOrInherited(user) ? 'text-ThemeBgDisabled' : ''}>
            {user.isRequestToJoin ? undefined : user.lastLogin ? formatDate(user.lastLogin, DATE.HUMANIZE) : 'never'}
          </span>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      sortingFn: (a, b) => naturalSort(getStatus(a.original), getStatus(b.original)),
      cell: ({ row }) => {
        const user = row.original;
        if (user.isRequestToJoin) {
          return (
            <Button
              className='text-nowrap p-0'
              color='link'
              size='sm'
              onClick={() => user.invitation && setRequestToJoinModal(user.invitation)}
            >
              View request
            </Button>
          );
        }

        return (
          <div className={`${isCurrentUserOrInherited(user) ? 'text-ThemeBgDisabled' : ''}`}>{getStatus(user)}</div>
        );
      },
    },
  ];

  const noResults = filteredUsers.length === 0;
  let noUsersMessage = '';
  if (noResults) {
    noUsersMessage =
      filterPermission || filterStatus || searchText
        ? 'No users match your criteria'
        : 'You have no users assigned to this reporting level';
  }

  const handleDownload = () => {
    const headers = ['Name', 'Email', 'Invite date', 'Status', 'Subsidiary', 'Permissions'];
    const values = filteredUsers.reduce((rows, user) => {
      const invitedDate = user.invitedDate ?? user.invitation?.created;
      const formattedInvitedDate = invitedDate ? formatDateUTC(invitedDate, DATE.DEFAULT_DASHES) : '';
      const status = getStatus(user);

      const permissions = (user.isActivated ? user.user?.permissions : user.invitation?.user.permissions) ?? [];
      permissions.forEach((permission) => {
        const initiative = initiativeTree.get(permission.initiativeId);
        if (!initiative) {
          return;
        }

        rows.push([
          user.name ?? '',
          user.email ?? '',
          formattedInvitedDate,
          status,
          initiative.name,
          permission.permissions.map((permission) => getRoleName(permission)).join(', '),
        ]);
      });

      return rows;
    }, [] as string[][]);
    exportToExcel({ headers, values, fileName: 'users' });
  };

  return (
    <>
      <div className='d-flex align-items-center'>
        <div className='flex-grow-1'>
          <Input
            placeholder={'Search for user...'}
            onChange={(e) => setSearchText(e.target.value)}
            value={searchText} />
        </div>
        <div className='mx-2'>
          <UserFilter
            filterStatus={filterStatus}
            handleStatusChange={handleStatusChange}
            filterPermission={filterPermission}
            handlePermissionChange={handlePermissionChange}
            filterReportingLevel={filterReportingLevel}
            handleReportingLevelChange={handleReportingLevelChange}
            defaultOptionText='All Users'
          />
        </div>
        {isDownloadable ? (
          <IconButton
            icon='fal fa-file-excel'
            onClick={handleDownload}
            tooltip='Export users. Report respects filters and will export only users visible in below list.'
            className='mr-2'
          />
        ) : null}
        <div className='ml-auto'>
          <Button
            outline
            color='primary'
            className={'text-nowrap'}
            onClick={toggleUserInvitationForm}
            disabled={isAddUserDisabled}
            data-testid='add-user-btn'
          >
            <i className={'fa fa-plus mr-2'} />
            <span>{btnText}</span>
          </Button>
        </div>
      </div>

      <div className='mt-3 user-table-container'>
        <Table
          columns={tableColumns}
          data={filteredUsers}
          pageSize={10}
        />
        {noResults ? <BasicAlert type='secondary' className='mt-2'>{noUsersMessage}</BasicAlert> : null}
      </div>

      {requestToJoinModal ? <RequestToJoinModal invitation={requestToJoinModal} handleReload={handleReload} toggle={() => setRequestToJoinModal(undefined)} /> : null}
    </>
  );
}

export default UserTable;
