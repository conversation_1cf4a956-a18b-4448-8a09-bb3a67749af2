/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import React from 'react';
import { RoutesInterface } from '@g17eco/types/routes';
import { UserManagementRoute } from '@apps/company-tracker/routes/UserManagementRoute';
import SystemLog from '@apps/company-tracker/components/system-log';
import { Switch, Route } from 'react-router-dom';
import { Loader } from '@g17eco/atoms/loader';
import { useAppSelector } from '@reducers/index';
import { rootAppPath, getRootAppPath } from './utils';
import { DownloadsRoute } from '@components/downloads/DownloadsRoute';
import { PPTXReportsRoute } from '@apps/company-tracker/components/downloads/pptx/PPTXReportsRoute';
import CustomReportsRoute from '@components/downloads/CustomReportsRoute';
import { adminRoute } from '@apps/company-tracker/routes/admin-dashboard/AdminDashboardRoute';
import { DataShareRoute } from '../data-share/DataShareRoute';
import { ReferralsRoute } from '../referrals/ReferralsRoute';
import { SurveyTemplatesRoute } from '../survey-template';
import { BulkImporting } from '@apps/company-tracker/components/bulk-importing';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { AppRoute } from '../AppRoute';
import { SubsidiaryUserDelegation } from '@apps/company-tracker/components/admin-dashboard/subsidiary-user-delegation';
import { InitiativePermissions } from '@services/permissions/InitiativePermissions';
import { InsightRoute } from './InsightRoute';
import { InitiativeStructureContainer } from '../initiative-structure/InitiativeStructureContainer';
import { CustomMetricManagementRoute } from '@apps/company-tracker/routes/CustomMetricManagementRoute';
import { CompanyTrackerIntegrationsRoute } from '@apps/company-tracker/routes/integrations/CompanyTrackerIntegrationsRoute';
import { IntegrationViewRoute } from '@apps/company-tracker/routes/integrations/IntegrationViewRoute';
import { AdminSettingsRoute } from '@apps/company-tracker/routes/AdminSettingsRoute';
import { CompanySettingsRoute } from '@apps/company-tracker/routes/CompanySettingsRoute';
import { BankingSettingsRoute } from '@apps/company-tracker/routes/BankingSettingsRoute';
import { REPORT_EDITOR_ROUTE_MAP } from '@apps/company-tracker/routes/report-editor/ReporEditorEntryRoute';
import { SURVEY } from '@constants/terminology';
import { ManageWorkgroupsRoute } from '@apps/company-tracker/components/manage-workgroups';
import { DocumentLibraryRoute } from '@apps/company-tracker/components/document-library/DocumentLibraryRoute';

const routeDefaults = {
  auth: true,
  getRootAppPath: getRootAppPath,
  requiresInitiativeId: true,
  appPermissionId: 'app_company_tracker',
}

export const companyTrackerInitiativeRoutes: RoutesInterface = {
  NAVIGATE_BY_MAP: {
    ...routeDefaults,
    id: 'navigation_map',
    label: 'Organisational structure',
    path: `/${rootAppPath}/map/:initiativeId`,
    icon: 'fa-sitemap',
    tooltip: 'Navigation Tree of the Reporting Level Dashboards',
    component: InitiativeStructureContainer,
  },
  NAVIGATE_BY_ARCHIVED: {
    ...routeDefaults,
    id: 'archived_map',
    label: 'Archived initiatives',
    path: `/${rootAppPath}/map/:initiativeId/archived`,
    icon: 'fa-sitemap',
    tooltip: 'Navigation Tree of the Archived Reporting Level Dashboards',
    component: InitiativeStructureContainer,
  },
  SUMMARY_CURRENT: {
    ...routeDefaults,
    id: 'summary',
    label: 'Insights',
    // @TODO this seems to be broken route, should be "/:page(current)?/", but fixing breaks InsightRoute...
    path: `/${rootAppPath}/summary/:initiativeId?/:page?(current)/:summaryPage?`,
    component: InsightRoute,
    navItemPermissionId: 'menu_summary',
    tooltip: 'Company profile dashboard',
  },
  CUSTOM_DASHBOARD: {
    ...routeDefaults,
    id: 'custom_dashboard',
    label: 'Custom Dashboard',
    // @TODO: remove surveyId since custom dashboard don't use it
    path: `/${rootAppPath}/summary/:initiativeId/:surveyId?/custom-dashboard/:dashboardId`,
    component: InsightRoute,
    navItemPermissionId: 'menu_summary',
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
  },
  SUMMARY: {
    ...routeDefaults,
    id: 'summary',
    label: 'Insights',
    path: `/${rootAppPath}/summary/:initiativeId?/:surveyId?/:summaryPage?`,
    component: InsightRoute,
    navItemPermissionId: 'menu_summary',
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
    tooltip: 'Company profile dashboard',
  },
  DOWNLOADS_CUSTOM: {
    ...routeDefaults,
    id: 'custom_downloads',
    label: 'Custom Downloads',
    path: `/${rootAppPath}/downloads/:initiativeId/custom/:action(manage|create)/:reportId?`,
    defaultParams: {
      action: 'manage',
    },
    component: CustomReportsRoute,
    appPermissionId: 'app_company_tracker',
    tooltip: 'Custom Downloads',
    requiresInitiativeId: true,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessDownloads,
  },
  DOWNLOADS_PPTX: {
    ...routeDefaults,
    id: 'pptx_downloads',
    label: 'AI Enhanced Sustainability Reports',
    tooltip: 'AI Enhanced Sustainability Reports',
    path: `/${rootAppPath}/downloads/:initiativeId/pptx-reports`,
    requiresInitiativeId: true,
    exact: true,
    component: PPTXReportsRoute,
    canAccessFeaturePermission: FeaturePermissions.canAccessDownloads,
  },
  DOWNLOADS: {
    ...routeDefaults,
    id: 'downloads',
    label: 'Downloads',
    path: `/${rootAppPath}/downloads/:initiativeId?/:surveyId?`,
    component: DownloadsRoute,
    appPermissionId: 'app_company_tracker',
    tooltip: 'Downloads',
    requiresInitiativeId: true,
    requiredUserRoles: InitiativePermissions.canAccessInsightsAndDownloadsRoles,
    canAccessFeaturePermission: FeaturePermissions.canAccessDownloads,
  },
  MANAGE_WORKGROUPS: {
    ...routeDefaults,
    id: 'manage_workgroups',
    label: 'Manage Workgroups',
    path: `/${rootAppPath}/admin/:initiativeId/manage-users/workgroups/:workgroupId?`,
    component: ManageWorkgroupsRoute,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  MANAGE_USERS: {
    ...routeDefaults,
    id: 'manage_users',
    label: 'Manage Users',
    path: `/${rootAppPath}/admin/:initiativeId/manage-users/:page?`,
    icon: 'fa-user',
    tooltip: 'Manage Users and Permissions',
    component: UserManagementRoute,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  INTEGRATIONS_VIEW: {
    ...routeDefaults,
    id: 'integrations_view',
    label: 'Integration',
    path: `/${rootAppPath}/integrations/:initiativeId/:code`,
    component: IntegrationViewRoute,
    navItemPermissionId: 'menu_integrations_view',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    tooltip: 'Company Integrations',
  },
  INTEGRATIONS: {
    ...routeDefaults,
    id: 'integrations',
    label: 'Integrations',
    path: `/${rootAppPath}/integrations/:initiativeId`,
    component: CompanyTrackerIntegrationsRoute,
    navItemPermissionId: 'menu_integrations',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    tooltip: 'Company Integrations',
  },
  ...REPORT_EDITOR_ROUTE_MAP,
  CUSTOM_METRICS: {
    ...routeDefaults,
    id: 'custom_metrics',
    label: 'Custom Metrics',
    path: `/${rootAppPath}/admin/:initiativeId?/custom-metrics/:groupId?/:view?`,
    component: CustomMetricManagementRoute,
    tooltip: 'Custom Metrics',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  DATA_SHARE_INITIATIVE: {
    ...routeDefaults,
    id: 'data_share_initiative',
    label: 'Data Share',
    path: `/${rootAppPath}/admin/:initiativeId/data-share/:shareId?`,
    component: DataShareRoute,
    tooltip: 'Data Share',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  REFERRALS: {
    ...routeDefaults,
    id: 'referrals',
    label: 'Referrals',
    path: `/${rootAppPath}/admin/:initiativeId/referrals`,
    component: ReferralsRoute,
    tooltip: 'Referrals',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  ADMIN_DASHBOARD: { ...routeDefaults, ...adminRoute },
  SYSTEM_LOG: {
    ...routeDefaults,
    id: 'system_log',
    label: 'System Log',
    path: `/${rootAppPath}/admin/:initiativeId/system-log`,
    component: SystemLog,
    tooltip: 'System Log',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessAdminDashboard,
  },
  SUBSIDIARY_USER_DELEGATION: {
    ...routeDefaults,
    id: 'subsidiary_user_delegation',
    label: 'User Delegation',
    path: `/${rootAppPath}/admin/:initiativeId/user/:userId/delegation`,
    component: SubsidiaryUserDelegation,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessBulkDelegation,
  },
  ACCOUNT_SETTINGS: {
    ...routeDefaults,
    id: 'account_settings',
    label: 'Account Settings',
    path: `/${rootAppPath}/admin/:initiativeId/account-settings/:page?`,
    component: CompanySettingsRoute,
    tooltip: 'Account Settings',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    defaultParams: { page: 'details' },
  },
  BANKING_SETTINGS: {
    ...routeDefaults,
    id: 'banking_settings',
    label: 'Banking Settings',
    path: `/${rootAppPath}/admin/:initiativeId/banking-settings`,
    component: BankingSettingsRoute,
    tooltip: 'Banking Settings',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  DOCUMENT_LIBRARY: {
    ...routeDefaults,
    id: 'document_library',
    label: 'Document library',
    path: `/${rootAppPath}/admin/:initiativeId/document-library`,
    component: DocumentLibraryRoute,
    requiredUserRoles: InitiativePermissions.canManageRoles,
  },
  ADMIN_SETTINGS: {
    ...routeDefaults,
    id: 'admin',
    label: 'Admin',
    path: `/${rootAppPath}/admin/:initiativeId`,
    component: AdminSettingsRoute,
    tooltip: 'Admin',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
  },
  BULK_IMPORTING: {
    ...routeDefaults,
    id: 'bulk_reporting',
    label: 'Bulk Reporting',
    path: `/${rootAppPath}/bulk-importing/:initiativeId`,
    component: BulkImporting,
    tooltip: 'Bulk Reporting',
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessBulkImporting,
  },
  SURVEY_TEMPLATES: {
    ...routeDefaults,
    id: 'survey_templates',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Templates`,
    path: `/${rootAppPath}/survey-templates/:initiativeId`,
    component: SurveyTemplatesRoute,
    tooltip: `${SURVEY.CAPITALIZED_SINGULAR} Templates`,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessSurveyTemplates,
  },
  SURVEY_TEMPLATES_VIEW: {
    ...routeDefaults,
    id: 'survey_templates_view',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Templates`,
    path: `/${rootAppPath}/survey-templates/:initiativeId/template/:templateId/:page`,
    component: SurveyTemplatesRoute,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessSurveyTemplates,
  },
  SURVEY_TEMPLATES_HISTORY: {
    ...routeDefaults,
    id: 'survey_templates_history',
    label: `${SURVEY.CAPITALIZED_SINGULAR} Templates History`,
    path: `/${rootAppPath}/survey-templates/:initiativeId/template/:templateId/history/:historyId/:page?`,
    component: SurveyTemplatesRoute,
    requiredUserRoles: InitiativePermissions.canManageRoles,
    hideOnMissingPermission: true,
    canAccessFeaturePermission: FeaturePermissions.canAccessSurveyTemplates,
  },
};

export const CompanyTrackerInitiativeRoute = () => {

  const initiativeState = useAppSelector((state) => state.initiative);
  if (!initiativeState.loaded) {
    return <Loader />;
  }

  // All of these must load initiatives
  return (
    <Switch>
      {Object.entries(companyTrackerInitiativeRoutes).map(([, route]) => (
        <Route
          key={`route_${route.id}`}
          path={route.path}
          exact={route.exact}
          render={() => <AppRoute route={route} />}
        />
      ))}
    </Switch>
  )
}
