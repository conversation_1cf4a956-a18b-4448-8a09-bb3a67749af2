import { ValueListPlain } from '../types/valueList';
import { g17ecoApi, transformResponse } from './g17ecoApi';

const valueListTag = 'value-list';
const valueListIdsTag = 'value-list-ids';

export const valueListApi = g17ecoApi
  .enhanceEndpoints({
    addTagTypes: [valueListTag, valueListIdsTag],
  })
  .injectEndpoints({
    endpoints: (builder) => ({
      getValueListById: builder.query<ValueListPlain, string>({
        transformResponse,
        query: (listId) => {
          return {
            url: `value-list/${listId}`,
            method: 'get',
          };
        },
        providesTags: (result, error, agrs) => [{ type: valueListTag, id: agrs }],
      }),
      getValueListByIds: builder.query<ValueListPlain[], string[]>({
        transformResponse,
        query: (ids) => {
          return {
            url: 'value-list/ids',
            method: 'post',
            data: {
              ids,
            },
          };
        },
        providesTags: (result, error, ids) => ids.map((id) => ({ type: valueListIdsTag, id })),
      }),
      getSharableValueListById: builder.query<
        ValueListPlain,
        { valueListId: string; dashboardId: string; token: string }
      >({
        transformResponse,
        query: ({ valueListId, dashboardId, token }) => {
          return {
            url: `o/value-list/${valueListId}/insight-dashboards/${dashboardId}/token/${token}`,
            method: 'get',
          };
        },
        providesTags: [valueListTag],
      }),
    }),
  });

export const { useGetValueListByIdQuery, useGetSharableValueListByIdQuery, useGetValueListByIdsQuery } = valueListApi;
