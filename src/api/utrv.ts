import { createApi } from '@reduxjs/toolkit/query/react';
import { ApiResponse } from '../types/api';
import { axiosBaseQuery } from './axiosQuery';
import { ConnectionUtrv } from '../types/universalTrackerValue';
import { Connection } from '../types/universalTracker';
import { transformResponse } from './g17ecoApi';
import { TKeyValue } from '@g17eco/types/file';
import { UniversalTrackerConnection, CalculationUtr, CalculationIntegrationUtr } from '@g17eco/types/utrv-connections';
import { SurveyModelMinData } from '@models/surveyData';
import { UtrvVariation } from '@g17eco/types/question';

interface ConnectionsData {
  connections: Connection[];
  utrvs: ConnectionUtrv[];
}
const UTRV_CONNECTIONS_TAG = 'utrv-connections';

export type Survey = Pick<SurveyModelMinData, '_id' | 'period' | 'effectiveDate' | 'initiativeId' | 'name' | 'scope'>;

export interface GetSecondaryConnectionsResponse {
  connections: UniversalTrackerConnection[];
  utrs: CalculationUtr[];
  integrationUtrs?: CalculationIntegrationUtr[];
  surveys: Survey[];
}

const UTRV_VARIATIONS_TAG = 'utrv-variations';

// Define a service using a base URL and expected endpoints
export const utrvApi = createApi({
  reducerPath: 'relatedApi',
  baseQuery: axiosBaseQuery(),
  tagTypes: ['utrv-related', 'target-baseline', UTRV_CONNECTIONS_TAG, UTRV_VARIATIONS_TAG],
  endpoints: (builder) => ({
    getConnections: builder.query<ConnectionsData, string>({
      transformResponse: (response: ApiResponse<ConnectionsData>) => response.data,
      query: (utrvId) => ({
        url: `/universal-tracker-values/${utrvId}/connections`,
        method: 'get',
      }),
    }),
    updateTargetBaseline: builder.mutation<string, TKeyValue & { utrvId: string }>({
      transformResponse,
      query: ({ utrvId, ...data }) => ({
        url: `/universal-tracker-values/${utrvId}/update-target-baseline`,
        method: 'patch',
        data,
      }),
      invalidatesTags: (result, error, arg) => [{ type: 'target-baseline', id: arg.utrvId }],
    }),
    getSecondaryConnections: builder.query<GetSecondaryConnectionsResponse, { utrvId: string }>({
      transformResponse,
      query: ({ utrvId }) => ({
        url: `/universal-tracker-values/${utrvId}/secondary-connections`,
        method: 'get',
      }),
      providesTags: (_result, _error, agrs) => [{ type: UTRV_CONNECTIONS_TAG, id: agrs.utrvId }],
    }),
    getUtrvVariations: builder.query<UtrvVariation[], { utrvId: string }>({
      transformResponse,
      query: ({ utrvId }) => ({
        url: `/universal-tracker-values/${utrvId}/variations`,
        method: 'get',
      }),
      providesTags: (_result, _error, agrs) => [{ type: UTRV_VARIATIONS_TAG, id: agrs.utrvId }],
    }),
  }),
});

export const { useGetConnectionsQuery, useUpdateTargetBaselineMutation, useGetSecondaryConnectionsQuery, useGetUtrvVariationsQuery } = utrvApi;
