/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import * as actionTypes from '../constants/action-types';
import { AnyAction } from 'redux';
import { ReduxStateLoadable } from '@reducers/types';
import { SurveySummary } from '../types/survey';


type State = ReduxStateLoadable<SurveySummary[], SurveySummary[]> & { initiativeId?: string, orgId?: string };

const createInitialState = (): State => ({
  data: [],
  loaded: false,
  errored: false,
  errorMessage: '',
  initiativeId: undefined,
  orgId: undefined
})

const surveyListSummary = (
  state = createInitialState(),
  action: AnyAction,
): State => {
  const lastUpdated = Date.now();
  switch (action.type) {
    case actionTypes.LOADING_SURVEY_LIST_SUMMARY:
      return {
        ...state,
        data: [],
        loaded: false,
        initiativeId: action.initiativeId,
        orgId: action.orgId,
        errored: false,
        lastUpdated
      };
    case actionTypes.UNLOADING_SURVEY_LIST_SUMMARY:
      return createInitialState();
    case actionTypes.ERROR_SURVEY_LIST_SUMMARY:
      return {
        ...state,
        data: [],
        loaded: false,
        errored: true,
        lastUpdated,
        errorMessage: action.errorMessage
      };
    case actionTypes.LOADED_SURVEY_LIST_SUMMARY:
      return {
        ...state,
        data: action.data,
        initiativeId: action.initiativeId,
        orgId: action.orgId,
        loaded: true,
        errored: false,
        lastUpdated,
      };
    case actionTypes.UPDATE_SURVEY_LIST_SUMMARY:
      return {
        ...state,
        data: action.data,
      };
    default:
      return state;
  }
};

export default surveyListSummary;
