/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import './styles.scss';
import { useLayoutEffect, useRef, useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { defaultConfig } from '@features/rich-text-editor/constants';
import { EditorState } from 'lexical';
import { Button } from 'reactstrap';

interface TextCollapseProps {
  isHidden: boolean;
  height: number;
  defaultHeight: number;
}

const useTextCollapse = ({ isHidden, height, defaultHeight }: TextCollapseProps) => {
  const isTruncated = height > defaultHeight;

  return {
    height: isTruncated && isHidden ? `${defaultHeight}px` : `${height}px`,
    className: isTruncated ? `is-truncated ${isHidden ? 'collapsed' : 'expanded'}` : '',
    isTruncated
  };
};

const initialConfig = {
  ...defaultConfig,
  namespace: 'MyEditor',
  editable: false,
};

const EditorContent = () => (
  <>
    <RichTextPlugin
      contentEditable={<ContentEditable className='' />}
      placeholder={null}
      ErrorBoundary={LexicalErrorBoundary}
    />
    <ListPlugin />
  </>
);

const ReadMoreButton = ({ isHidden, onToggle }: { isHidden: boolean; onToggle: () => void }) => (
  <Button color='link-secondary' onClick={onToggle}>
    <div className='text-sm py-1'>
      <i className={`fa fa-angle-${isHidden ? 'down' : 'up'} mr-1 text-sm`} />
      {isHidden ? 'Read more' : 'Read less'}
    </div>
  </Button>
);

interface Props {
  editorState: EditorState;
  defaultHeight?: number;
  enableExpansion?: boolean;
}

export const ReadonlyRichTextEditor = ({
  editorState,
  defaultHeight = 150,
  enableExpansion = true
}: Props) => {
  const [currentHeight, setHeight] = useState(0);
  const [isHidden, setHidden] = useState(true);
  const editorRef = useRef<HTMLDivElement | null>(null);

  useLayoutEffect(() => {
    if (editorRef.current && enableExpansion) {
      setHeight(editorRef.current.scrollHeight || 0);
    }
  }, [editorState, enableExpansion]);

  const { height, className, isTruncated } = useTextCollapse({
    isHidden,
    height: currentHeight,
    defaultHeight,
  });

  const toggleExpansion = () => setHidden(prev => !prev);

  const containerClassName = enableExpansion
    ? 'expandable-rich-text-editor'
    : '';

  const editorClassName = `editor-container read-only position-relative ${enableExpansion ? className : ''}`.trim();

  const editorStyle = enableExpansion ? { height } : undefined;

  return (
    <LexicalComposer initialConfig={{ ...initialConfig, editorState }}>
      <div className={containerClassName}>
        <div
          ref={enableExpansion ? editorRef : undefined}
          className={editorClassName}
          style={editorStyle}
        >
          <EditorContent />
        </div>
        {isTruncated && enableExpansion && (
          <ReadMoreButton isHidden={isHidden} onToggle={toggleExpansion} />
        )}
      </div>
    </LexicalComposer>
  );
};
