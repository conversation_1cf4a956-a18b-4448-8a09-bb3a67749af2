/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import './styles.scss';
import { useLayoutEffect, useRef, useState } from 'react';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { defaultConfig } from '@features/rich-text-editor/constants';
import { EditorState } from 'lexical';
import { Button } from 'reactstrap';

interface TextCollapseProps {
  isHidden: boolean;
  height: number;
  defaultHeight?: number;
}

const useTextCollapse = (props: TextCollapseProps) => {
  const { isHidden, height, defaultHeight = 150 } = props;
  const isTruncatedText = height > defaultHeight;

  const getHeight = () => {
    switch (true) {
      case (isTruncatedText && isHidden):
        return `${defaultHeight}px`;
      default:
        return `${height}px`;
    }
  };

  const getClass = () => {
    if (!isTruncatedText) {
      return '';
    }
    if (isHidden) {
      return 'is-truncated collapsed';
    }
    return 'is-truncated expanded';
  };

  return { height: getHeight(), className: getClass(), isTruncated: isTruncatedText };
};

const initialConfig = {
  ...defaultConfig,
  namespace: 'MyEditor',
  editable: false,
};

interface Props {
  editorState: EditorState;
  defaultHeight?: number;
  enableExpansion?: boolean;
}

export const ReadonlyRichTextEditor = ({
  editorState,
  defaultHeight = 150,
  enableExpansion = true
}: Props) => {
  const [currentHeight, setHeight] = useState(0);
  const [isHidden, setHidden] = useState(true);
  const editorRef = useRef<HTMLDivElement | null>(null);

  useLayoutEffect(() => {
    if (editorRef.current && enableExpansion) {
      setHeight(editorRef.current.scrollHeight || 0);
    }
  }, [editorState, enableExpansion]);

  const { height, className, isTruncated } = useTextCollapse({
    isHidden,
    height: currentHeight,
    defaultHeight,
  });

  const ReadMoreButton = () => {
    const text = isHidden ? 'Read more' : 'Read less';
    const icon = isHidden ? 'fa-angle-down' : 'fa-angle-up';
    return (
      <Button color='link-secondary' onClick={() => setHidden((prev) => !prev)}>
        <div className='text-sm py-1'>
          <i className={`fa ${icon} mr-1 text-sm`} />
          {text}
        </div>
      </Button>
    );
  };

  if (!enableExpansion) {
    return (
      <LexicalComposer initialConfig={{ ...initialConfig, editorState }}>
        <div className='editor-container read-only position-relative'>
          <RichTextPlugin
            contentEditable={<ContentEditable className='' />}
            placeholder={null}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <ListPlugin />
        </div>
      </LexicalComposer>
    );
  }

  return (
    <LexicalComposer initialConfig={{ ...initialConfig, editorState }}>
      <div className='expandable-rich-text-editor'>
        <div
          ref={editorRef}
          className={`editor-container read-only position-relative ${className}`}
          style={{ height: enableExpansion ? height : 'auto' }}
        >
          <RichTextPlugin
            contentEditable={<ContentEditable className='' />}
            placeholder={null}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <ListPlugin />
        </div>
        {isTruncated && enableExpansion ? <ReadMoreButton /> : null}
      </div>
    </LexicalComposer>
  );
};
