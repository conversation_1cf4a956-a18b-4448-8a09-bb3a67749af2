/*
 * Copyright (c) 2025. World Wide Generation Ltd
 */

import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ReadonlyRichTextEditor } from './ReadonlyRichTextEditor';
import { createEditor } from 'lexical';
import { defaultConfig } from './constants';

describe('ReadonlyRichTextEditor', () => {
  const createEditorState = (text: string) => {
    const editor = createEditor(defaultConfig);
    const editorState = editor.parseEditorState(
      JSON.stringify({
        root: {
          children: [
            {
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text,
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              type: 'paragraph',
              version: 1,
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      })
    );
    return editorState;
  };

  it('renders basic readonly editor', () => {
    const editorState = createEditorState('Some text content');

    render(<ReadonlyRichTextEditor editorState={editorState} />);

    expect(screen.getByText('Some text content')).toBeInTheDocument();
  });

  it('renders with correct DOM structure', () => {
    const editorState = createEditorState('Some text content');

    const { container } = render(<ReadonlyRichTextEditor editorState={editorState} />);

    expect(container.querySelector('.editor-container.read-only')).toBeInTheDocument();
    expect(screen.getByText('Some text content')).toBeInTheDocument();
  });

  it('renders without any expansion-related elements', () => {
    const editorState = createEditorState('Some text content');

    render(<ReadonlyRichTextEditor editorState={editorState} />);

    expect(screen.queryByText('Read more')).not.toBeInTheDocument();
    expect(screen.queryByText('Read less')).not.toBeInTheDocument();
  });
});
