import { FormGroup, Input, Label } from 'reactstrap';

interface SwitchGroupProps {
  filterKey: string;
  label: string;
  checked: boolean | undefined;
  handleToggle: (key: string) => (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const SwitchGroup = ({ filterKey, label, checked, handleToggle }: SwitchGroupProps) => {
  return (
    <FormGroup switch className='d-flex align-items-center gap-2'>
      <Input type='switch' role='switch' checked={checked} onChange={handleToggle(filterKey)} />
      <Label className='text-md' check>
        {label}
      </Label>
    </FormGroup>
  );
};
