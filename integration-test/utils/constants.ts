export const SURVEY_TYPES = ['Annually', 'Monthly', 'Quarterly'];
export const SURVEY_YEARS = ['2024', '2023', '2022'];
export const SURVEY_VERSION = ['2020', '2022'];
export const METRICS_TYPE = ['Text', 'Number', 'Yes/No', 'Date'];
export const METRICS_UNIT = ['None', 'Time', 'Area', 'Length', 'Mass', 'Energy', 'CO2 Emissions', 'Currency'];

export const PRODUCTS = {
  COMPANY_TRACKER: {
    code: 'company-tracker',
    label: 'Company Tracker'
  }
};

export const MESSAGES = {
  INVALID_SURVEY_INFORMATION: 'A Report already exists for the selected Organization, Level and Period. Please update your settings.',
  NO_SELECT_METRICS: 'No metrics have been selected'
};

export const BUTTON = {
  CREATE: 'Create',
  CLOSE: 'Close',
  SUBMIT: 'Submit',
  COMPLETED: 'Completed',
  DELETE: 'Delete',
  SAVE: 'Save',
  ACCEPT: 'Accept',
  UPDATE: 'Update',
  GO_BACK_TEMPLATE_DASHBOARD: 'Go back to template dashboard',
};

export const LABEL = {
  STANDARDS_FRAMEWORKS_DROPDOWN: 'Standards and Frameworks',
  SDG_MODULES: 'Sustainable Development Goals'
};

export const PLACEHOLDER = {
  KEY_IN_VALUE: 'Key in value',
};

export const CUSTOM_METRIC = {
  CREATE_CUSTOM_METRIC: 'Create a custom metric',
  DELETE_CUSTOM_METRIC: 'Delete custom metric'
};

export const USER_INVITATION = {
  REVOKE_INVITATION: 'Revoke invitation',
};

export const TESTID = {
  APP: {
    APP_NAVIGATION_DESKTOP: 'app-navigation-desktop',
    APP_NAVIGATION_MOBILE: 'app-navigation-mobile',
    APP_BREADCRUMB: 'app-breadcrumb'
  },
  COMPANY: {
    ROOT_SWITCHER: 'root-switcher',
    COMPANY_DESCRIPTION_INPUT: 'company-description-input',
    COMPANY_DESCRIPTION_EDIT_BUTTON: 'company-description-edit-btn',
    COMPANY_DESCRIPTION_SAVE_BUTTON: 'company-description-save-btn',
    COMPANY_MISSION_STATEMENT_INPUT: 'company-missionStatement-input',
    COMPANY_MISSION_STATEMENT_EDIT_BUTTON: 'company-missionStatement-edit-btn',
    COMPANY_MISSION_STATEMENT_SAVE_BUTTON: 'company-missionStatement-save-btn',
  },
  CUSTOM_METRIC_GROUP: {
    CREATE_GROUP_BUTTON: 'create-metric-group-btn',
    CREATE_GROUP_CARD: 'create-metric-group-card',
    METRIC_GROUP_NAME_INPUT: 'metric-group-name-input',
    METRIC_GROUP_DESCRIPTION_INPUT: 'metric-group-description-input',
    DUPLICATE_METRIC_GROUP_INPUT: 'duplicate-metric-group-input',
    DUPLICATE_METRIC_GROUP_SUBMIT_BUTTON: 'duplicate-metric-group-submit-btn',
    SEARCH_METRICS_INPUT: 'search-metrics-input',
    TABLE_METRICS: 'table-metrics',
    CREATE_CUSTOM_METRIC_BUTTON: 'create-custom-metric-btn',
    DELETE_METRIC_GROUP_BUTTON: 'delete-metric-group-btn'
  },
  CUSTOM_METRIC: {
    TABLE_CUSTOM_METRICS: 'table-custom-metrics',
    SEARCH_UNUSED_CUSTOM_METRICS_INPUT: 'search-unused-custom-metrics-input',
    SEARCH_USED_CUSTOM_METRICS_INPUT: 'search-used-custom-metrics-input',
    CUSTOM_METRIC_NAME_INPUT: 'custom-metric-name-input',
    CUSTOM_METRIC_VALUE_LABEL_INPUT: 'custom-metric-valueLabel-input',
    CUSTOM_METRIC_TYPE_CODE_INPUT: 'custom-metric-typeCode-input',
    CUSTOM_METRIC_INSTRUCTIONS_INPUT: 'custom-metric-instructions-input',
    METRIC_VALUE_TYPE_SELECT: 'metric-value-type-select',
    METRIC_UNIT_TYPE_SELECT: 'metric-unit-type-select',
    CUSTOM_METRIC_FORM_SUBMIT_BUTTON: 'custom-metric-form-submit-btn',
    CUSTOM_METRIC_EDIT_BUTTON: 'custom-metric-edit-btn'
  },
  SURVEY: {
    SURVEY_LIST: 'survey-list',
    SURVEY_QUESTION_LIST: 'survey-question-list',
    SURVEY_DELEGATION_SWITCHER: 'survey-delegation-switcher',
    SURVEY_TYPE_SWITCHER: 'survey-type-switcher',
    SURVEY_CREATE_SWITCHER: 'survey-create-switcher',
    NO_SURVEYS_MESSAGE: 'no-report-message',
    SURVEY_VERSION_SELECT: 'survey-version-select',
    SURVEY_TYPE_SELECT: 'survey-type-select',
    SURVEY_MONTH_SELECT: 'survey-month-select',
    SURVEY_YEAR_SELECT: 'survey-year-select',
    SURVEY_CONFIGS_SUBMIT_BUTTON: 'survey-configs-submit-btn',
    DELETE_SURVEY_CONFIRM_BUTTON: 'delete-survey-confirm-btn',
    COMBINED_SURVEY_NAME_INPUT: 'combined-survey-name-input',
    COMBINED_SURVEY_SUBMIT_BUTTON: 'combined-survey-submit-btn',
    SURVEY_SCOPES_SWITCHER: 'survey-scopes-switcher',
    BULK_ACTION_TOOLBAR: 'bulk-action-toolbar',
    BULK_ACTION_ADD_TAG_BUTTON: 'bulk-action-add-tag-btn',
    SURVEY_COMPLETE_BUTTON: 'survey-complete-btn',
    SURVEY_CONFIRM_COMPLETE_BUTTON: 'survey-confirm-complete-btn',
    SURVEY_OVERVIEW_SEARCH_METRICS: 'survey-overview-search-metrics',
    SURVEY_OVERVIEW_BREADCRUMB_ITEM: 'survey-overview-breadcrumb-item',
    SURVEY_NAME_INPUT: 'survey-name-input'
  },
  SURVEY_TEMPLATE: {
    SURVEY_TEMPLATE_NAME_INPUT: 'survey-template-name-input',
    SURVEY_TEMPLATE_REPORT_NAME_INPUT: 'survey-template-report-name-input',
    SURVEY_TEMPLATE_SUBMIT_BUTTON: 'survey-template-submit-btn',
    SURVEY_TEMPLATE_BULK_CREATE_BUTTON: 'survey-template-bulk-create-btn',
    COMBINED_TEMPLATE_NAME_INPUT: 'combined-template-name-input',
    COMBINED_TEMPLATE_SURVEY_NAME_INPUT: 'combined-template-survey-name-input',
    COMBINED_SURVEY_TEMPLATE_SUBMIT_BUTTON: 'combined-survey-template-submit-btn'
  },
  TAG: {
    TAG_LIST: 'tag-list',
    TAG_LIST_ITEM: 'tag-list-item',
    MANAGE_TAG_BUTTON: 'manage-tag-btn',
    ADD_TAG_BUTTON: 'add-tag-btn',
    CREATE_TAG_BUTTON: 'create-tag-btn',
    DELETE_TAG_BUTTON: 'delete-tag-btn',
    TAG_NAME_INPUT: 'tag-name-input'
  },
  MODAL: {
    COMFIRM_DELETE_BUTTON: 'confirm-delete-btn'
  },
  QUESTION: {
    QUESTION_MARK_NR_BUTTON: 'question-mark-nr-btn',
    QUESTION_MARK_NA_BUTTON: 'question-mark-na-btn',
    QUESTION_PRIVACY_BUTTON: 'question-privacy-btn',
    QUESTION_REJECT_BUTTON: 'question-reject-btn',
    QUESTION_SUBMIT_BUTTON: 'question-submit-btn',
    QUESTION_SUBMIT_VERIFY_BUTTON: 'question-submit-verify-btn',
    QUESTION_VERIFY_BUTTON: 'question-verify-btn',
    QUESTION_EXPAND_ALL_BUTTON: 'question-expand-all-btn',
    QUESTION_STATUS_BADGE: 'question-status-badge',
  },
  USER: {
    USER_PROFILE_SUBMIT_BUTTON: 'user-profile-submit-btn',
    ADD_USER_BUTTON: 'add-user-btn',
    ADD_USER_EMAIL_INPUT: 'add-user-email-input',
    INVITE_ADDITIONAL_USER_BUTTON: 'invite-additional-user-btn',
    INVITE_USERS_BUTTON: 'invite-users-btn',
    MANAGE_USERS_TABLE: 'manage-users-table',
    SUPPORT_USERS_TABLE: 'support-users-table',
    USER_PROFILE_NAV_BUTTON: 'user-profile-nav-btn'
  }
};
